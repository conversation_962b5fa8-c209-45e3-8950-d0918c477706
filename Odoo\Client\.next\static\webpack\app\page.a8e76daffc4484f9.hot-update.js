"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/testimonials-section.tsx":
/*!*********************************************************!*\
  !*** ./src/components/landing/testimonials-section.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TestimonialsSection: () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ TestimonialsSection auto */ \nconst testimonials = [\n    {\n        name: 'Sarah Chen',\n        role: 'UX Designer',\n        avatar: '/avatars/sarah.jpg',\n        rating: 5,\n        content: \"I learned Spanish conversation skills in exchange for teaching Figma. The process was so smooth and natural - like having a coffee chat with a friend!\",\n        skills: [\n            'Figma',\n            'Design Systems'\n        ],\n        learned: [\n            'Spanish',\n            'Photography'\n        ]\n    },\n    {\n        name: 'Marcus Rodriguez',\n        role: 'Software Developer',\n        avatar: '/avatars/marcus.jpg',\n        rating: 5,\n        content: \"SkillSwap connected me with amazing people. I taught React and learned guitar basics. The AI matching is incredibly accurate!\",\n        skills: [\n            'React',\n            'Node.js'\n        ],\n        learned: [\n            'Guitar',\n            'Music Theory'\n        ]\n    },\n    {\n        name: 'Emily Johnson',\n        role: 'Marketing Manager',\n        avatar: '/avatars/emily.jpg',\n        rating: 5,\n        content: \"As someone who's always wanted to learn coding, finding a patient teacher through SkillSwap was a game-changer. I taught social media marketing in return.\",\n        skills: [\n            'Social Media',\n            'Content Strategy'\n        ],\n        learned: [\n            'Python',\n            'Data Analysis'\n        ]\n    },\n    {\n        name: 'David Kim',\n        role: 'Chef',\n        avatar: '/avatars/david.jpg',\n        rating: 5,\n        content: \"I never thought I'd learn graphic design, but SkillSwap made it possible. Taught cooking in exchange for Photoshop skills. Win-win!\",\n        skills: [\n            'Cooking',\n            'Recipe Development'\n        ],\n        learned: [\n            'Photoshop',\n            'Branding'\n        ]\n    },\n    {\n        name: 'Anna Kowalski',\n        role: 'Financial Analyst',\n        avatar: '/avatars/anna.jpg',\n        rating: 5,\n        content: \"The platform is incredibly user-friendly. I've completed 5 skill swaps and made genuine friends along the way. The community is amazing!\",\n        skills: [\n            'Excel',\n            'Financial Modeling'\n        ],\n        learned: [\n            'Web Development',\n            'SEO'\n        ]\n    },\n    {\n        name: 'James Thompson',\n        role: 'Teacher',\n        avatar: '/avatars/james.jpg',\n        rating: 5,\n        content: \"Teaching language skills while learning digital marketing has been transformative for my career. SkillSwap makes peer learning feel natural.\",\n        skills: [\n            'French',\n            'Language Teaching'\n        ],\n        learned: [\n            'Digital Marketing',\n            'Analytics'\n        ]\n    }\n];\nfunction TestimonialsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-2xl text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                            children: \"Loved by skill enthusiasts worldwide\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"mt-6 text-lg leading-8 text-gray-600\",\n                            children: \"Real stories from real people who've transformed their skills through our platform.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3\",\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 40\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden rounded-2xl bg-gray-50 p-8 h-full transition-all duration-300 hover:bg-white hover:shadow-lg hover:scale-105 border border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StarIcon, {\n                                                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.content,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-green-600 uppercase tracking-wide\",\n                                                        children: \"Taught\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                                        children: testimonial.skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                                                                children: skill\n                                                            }, skill, false, {\n                                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                                lineNumber: 120,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-blue-600 uppercase tracking-wide\",\n                                                        children: \"Learned\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                                        children: testimonial.learned.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                children: skill\n                                                            }, skill, false, {\n                                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm\",\n                                                children: testimonial.name.split(' ').map((n)=>n[0]).join('')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                        children: testimonial.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: testimonial.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, this)\n                        }, testimonial.name, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mx-auto mt-16 max-w-4xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600 p-8 text-center text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-8 sm:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: \"4.9/5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-100\",\n                                            children: \"Average rating\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: \"95%\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-100\",\n                                            children: \"Successful swaps\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold\",\n                                            children: \"2.5 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-100\",\n                                            children: \"Average match time\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/testimonials-section.tsx\n"));

/***/ })

});