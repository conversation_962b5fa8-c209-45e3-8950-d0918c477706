/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(rsc)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/auth-provider.tsx */ \"(rsc)/./src/providers/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/socket-provider.tsx */ \"(rsc)/./src/providers/socket-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNIYWNrYXRob24lNUMlNUNPZG9vJTVDJTVDT2RvbyU1QyU1Q0NsaWVudCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDSGFja2F0aG9uJTVDJTVDT2RvbyU1QyU1Q09kb28lNUMlNUNDbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDSGFja2F0aG9uJTVDJTVDT2RvbyU1QyU1Q09kb28lNUMlNUNDbGllbnQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUN0b2FzdGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0hhY2thdGhvbiU1QyU1Q09kb28lNUMlNUNPZG9vJTVDJTVDQ2xpZW50JTVDJTVDc3JjJTVDJTVDcHJvdmlkZXJzJTVDJTVDYXV0aC1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0hhY2thdGhvbiU1QyU1Q09kb28lNUMlNUNPZG9vJTVDJTVDQ2xpZW50JTVDJTVDc3JjJTVDJTVDcHJvdmlkZXJzJTVDJTVDc29ja2V0LXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNvY2tldFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBcUk7QUFDckk7QUFDQSw4S0FBMkk7QUFDM0k7QUFDQSxrTEFBK0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxIYWNrYXRob25cXFxcT2Rvb1xcXFxPZG9vXFxcXENsaWVudFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFx1aVxcXFx0b2FzdGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcSGFja2F0aG9uXFxcXE9kb29cXFxcT2Rvb1xcXFxDbGllbnRcXFxcc3JjXFxcXHByb3ZpZGVyc1xcXFxhdXRoLXByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU29ja2V0UHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxIYWNrYXRob25cXFxcT2Rvb1xcXFxPZG9vXFxcXENsaWVudFxcXFxzcmNcXFxccHJvdmlkZXJzXFxcXHNvY2tldC1wcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"86825a0c7a02\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcSGFja2F0aG9uXFxPZG9vXFxPZG9vXFxDbGllbnRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjg2ODI1YTBjN2EwMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../providers/auth-provider */ \"(rsc)/./src/providers/auth-provider.tsx\");\n/* harmony import */ var _providers_socket_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../providers/socket-provider */ \"(rsc)/./src/providers/socket-provider.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/toaster */ \"(rsc)/./src/components/ui/toaster.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: 'SkillSwap - Exchange Skills, Build Connections',\n    description: 'A frictionless platform for exchanging skills and building meaningful connections through knowledge sharing.',\n    keywords: [\n        'skill exchange',\n        'learning',\n        'networking',\n        'professional development'\n    ],\n    authors: [\n        {\n            name: 'SkillSwap Team'\n        }\n    ],\n    creator: 'SkillSwap Platform',\n    publisher: 'SkillSwap',\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL('https://skillswap.com'),\n    alternates: {\n        canonical: '/'\n    },\n    openGraph: {\n        title: 'SkillSwap - Exchange Skills, Build Connections',\n        description: 'A frictionless platform for exchanging skills and building meaningful connections through knowledge sharing.',\n        url: 'https://skillswap.com',\n        siteName: 'SkillSwap',\n        locale: 'en_US',\n        type: 'website'\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'SkillSwap - Exchange Skills, Build Connections',\n        description: 'A frictionless platform for exchanging skills and building meaningful connections through knowledge sharing.',\n        creator: '@skillswap'\n    },\n    verification: {\n        google: 'google-site-verification-token'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} h-full antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_socket_provider__WEBPACK_IMPORTED_MODULE_3__.SocketProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"min-h-full\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__.Toaster, {}, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster),
/* harmony export */   toast: () => (/* binding */ toast)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\ui\\toaster.tsx",
"Toaster",
);const toast = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\ui\\toaster.tsx",
"toast",
);

/***/ }),

/***/ "(rsc)/./src/providers/auth-provider.tsx":
/*!*****************************************!*\
  !*** ./src/providers/auth-provider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuthContext: () => (/* binding */ useAuthContext)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\auth-provider.tsx",
"AuthProvider",
);const useAuthContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuthContext() from the server but useAuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\auth-provider.tsx",
"useAuthContext",
);

/***/ }),

/***/ "(rsc)/./src/providers/socket-provider.tsx":
/*!*******************************************!*\
  !*** ./src/providers/socket-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),
/* harmony export */   useSocket: () => (/* binding */ useSocket)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SocketProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SocketProvider() from the server but SocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\socket-provider.tsx",
"SocketProvider",
);const useSocket = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSocket() from the server but useSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\socket-provider.tsx",
"useSocket",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(ssr)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/auth-provider.tsx */ \"(ssr)/./src/providers/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/socket-provider.tsx */ \"(ssr)/./src/providers/socket-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast auto */ \n\n\n\n// Simple toast store (in a real app, you might use Zustand or similar)\nconst toastStore = {\n    toasts: [],\n    addToast: ()=>{},\n    removeToast: ()=>{}\n};\nconst icons = {\n    success: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    error: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    warning: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    info: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n};\nconst colors = {\n    success: {\n        bg: 'bg-green-50',\n        border: 'border-green-200',\n        icon: 'text-green-600',\n        title: 'text-green-900',\n        message: 'text-green-700'\n    },\n    error: {\n        bg: 'bg-red-50',\n        border: 'border-red-200',\n        icon: 'text-red-600',\n        title: 'text-red-900',\n        message: 'text-red-700'\n    },\n    warning: {\n        bg: 'bg-yellow-50',\n        border: 'border-yellow-200',\n        icon: 'text-yellow-600',\n        title: 'text-yellow-900',\n        message: 'text-yellow-700'\n    },\n    info: {\n        bg: 'bg-blue-50',\n        border: 'border-blue-200',\n        icon: 'text-blue-600',\n        title: 'text-blue-900',\n        message: 'text-blue-700'\n    }\n};\nfunction Toaster() {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toaster.useEffect\": ()=>{\n            toastStore.addToast = ({\n                \"Toaster.useEffect\": (toast)=>{\n                    const id = Math.random().toString(36).substr(2, 9);\n                    const newToast = {\n                        ...toast,\n                        id\n                    };\n                    setToasts({\n                        \"Toaster.useEffect\": (prev)=>[\n                                ...prev,\n                                newToast\n                            ]\n                    }[\"Toaster.useEffect\"]);\n                    // Auto remove after duration\n                    setTimeout({\n                        \"Toaster.useEffect\": ()=>{\n                            toastStore.removeToast(id);\n                        }\n                    }[\"Toaster.useEffect\"], toast.duration || 5000);\n                }\n            })[\"Toaster.useEffect\"];\n            toastStore.removeToast = ({\n                \"Toaster.useEffect\": (id)=>{\n                    setToasts({\n                        \"Toaster.useEffect\": (prev)=>prev.filter({\n                                \"Toaster.useEffect\": (toast)=>toast.id !== id\n                            }[\"Toaster.useEffect\"])\n                    }[\"Toaster.useEffect\"]);\n                }\n            })[\"Toaster.useEffect\"];\n        }\n    }[\"Toaster.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 w-full max-w-sm space-y-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n            children: toasts.map((toast)=>{\n                const Icon = icons[toast.type];\n                const color = colors[toast.type];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: 100,\n                        scale: 0.95\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        x: 100,\n                        scale: 0.95\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: `relative rounded-lg border p-4 shadow-lg ${color.bg} ${color.border}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: `h-5 w-5 mt-0.5 mr-3 flex-shrink-0 ${color.icon}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-sm font-medium ${color.title}`,\n                                        children: toast.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 19\n                                    }, this),\n                                    toast.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `mt-1 text-sm ${color.message}`,\n                                        children: toast.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>toastStore.removeToast(toast.id),\n                                className: `ml-3 inline-flex rounded-md p-1.5 hover:bg-black/5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${color.icon}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Dismiss\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 15\n                    }, this)\n                }, toast.id, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n// Export toast function for use throughout the app\nconst toast = {\n    success: (title, message, duration)=>toastStore.addToast({\n            type: 'success',\n            title,\n            message,\n            duration\n        }),\n    error: (title, message, duration)=>toastStore.addToast({\n            type: 'error',\n            title,\n            message,\n            duration\n        }),\n    warning: (title, message, duration)=>toastStore.addToast({\n            type: 'warning',\n            title,\n            message,\n            duration\n        }),\n    info: (title, message, duration)=>toastStore.addToast({\n            type: 'info',\n            title,\n            message,\n            duration\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-auth.ts":
/*!*******************************!*\
  !*** ./src/hooks/use-auth.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var _store_auth_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../store/auth-store */ \"(ssr)/./src/store/auth-store.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \nfunction useAuth() {\n    const { user, isAuthenticated, isLoading, login, logout, updateProfile } = (0,_store_auth_store__WEBPACK_IMPORTED_MODULE_0__.useAuthStore)();\n    return {\n        user,\n        isAuthenticated,\n        isLoading,\n        login,\n        logout,\n        updateProfile\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlLWF1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRWtEO0FBRTNDLFNBQVNDO0lBQ2QsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLGVBQWUsRUFBRUMsU0FBUyxFQUFFQyxLQUFLLEVBQUVDLE1BQU0sRUFBRUMsYUFBYSxFQUFFLEdBQUdQLCtEQUFZQTtJQUV2RixPQUFPO1FBQ0xFO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXEhhY2thdGhvblxcT2Rvb1xcT2Rvb1xcQ2xpZW50XFxzcmNcXGhvb2tzXFx1c2UtYXV0aC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCB7IHVzZUF1dGhTdG9yZSB9IGZyb20gJy4uL3N0b3JlL2F1dGgtc3RvcmUnXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlQXV0aCgpIHtcclxuICBjb25zdCB7IHVzZXIsIGlzQXV0aGVudGljYXRlZCwgaXNMb2FkaW5nLCBsb2dpbiwgbG9nb3V0LCB1cGRhdGVQcm9maWxlIH0gPSB1c2VBdXRoU3RvcmUoKVxyXG5cclxuICByZXR1cm4ge1xyXG4gICAgdXNlcixcclxuICAgIGlzQXV0aGVudGljYXRlZCxcclxuICAgIGlzTG9hZGluZyxcclxuICAgIGxvZ2luLFxyXG4gICAgbG9nb3V0LFxyXG4gICAgdXBkYXRlUHJvZmlsZSxcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZUF1dGhTdG9yZSIsInVzZUF1dGgiLCJ1c2VyIiwiaXNBdXRoZW50aWNhdGVkIiwiaXNMb2FkaW5nIiwibG9naW4iLCJsb2dvdXQiLCJ1cGRhdGVQcm9maWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/auth-provider.tsx":
/*!*****************************************!*\
  !*** ./src/providers/auth-provider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuthContext: () => (/* binding */ useAuthContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../store/auth-store */ \"(ssr)/./src/store/auth-store.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuthContext auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nfunction AuthProvider({ children }) {\n    const setUser = (0,_store_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)({\n        \"AuthProvider.useAuthStore[setUser]\": (state)=>state.setUser\n    }[\"AuthProvider.useAuthStore[setUser]\"]);\n    const setLoading = (0,_store_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)({\n        \"AuthProvider.useAuthStore[setLoading]\": (state)=>state.setLoading\n    }[\"AuthProvider.useAuthStore[setLoading]\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check if user is authenticated on app load\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/auth/me', {\n                            credentials: 'include'\n                        });\n                        if (response.ok) {\n                            const { user } = await response.json();\n                            setUser(user);\n                        } else {\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error('Auth check failed:', error);\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        setUser,\n        setLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {},\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\providers\\\\auth-provider.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuthContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuthContext must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/socket-provider.tsx":
/*!*******************************************!*\
  !*** ./src/providers/socket-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/use-auth */ \"(ssr)/./src/hooks/use-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ SocketProvider,useSocket auto */ \n\n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    socket: null,\n    isConnected: false\n});\nfunction SocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, isAuthenticated } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocketProvider.useEffect\": ()=>{\n            if (isAuthenticated && user) {\n                // Initialize socket connection\n                const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001', {\n                    auth: {\n                        userId: user.id\n                    },\n                    autoConnect: true\n                });\n                socketInstance.on('connect', {\n                    \"SocketProvider.useEffect\": ()=>{\n                        console.log('Socket connected:', socketInstance.id);\n                        setIsConnected(true);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                socketInstance.on('disconnect', {\n                    \"SocketProvider.useEffect\": ()=>{\n                        console.log('Socket disconnected');\n                        setIsConnected(false);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                socketInstance.on('connect_error', {\n                    \"SocketProvider.useEffect\": (error)=>{\n                        console.error('Socket connection error:', error);\n                        setIsConnected(false);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                setSocket(socketInstance);\n                return ({\n                    \"SocketProvider.useEffect\": ()=>{\n                        socketInstance.disconnect();\n                        setSocket(null);\n                        setIsConnected(false);\n                    }\n                })[\"SocketProvider.useEffect\"];\n            } else {\n                // Disconnect socket if user is not authenticated\n                if (socket) {\n                    socket.disconnect();\n                    setSocket(null);\n                    setIsConnected(false);\n                }\n            }\n        }\n    }[\"SocketProvider.useEffect\"], [\n        isAuthenticated,\n        user?.id\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: {\n            socket,\n            isConnected\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\providers\\\\socket-provider.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\nfunction useSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n    if (context === undefined) {\n        throw new Error('useSocket must be used within a SocketProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXJzL3NvY2tldC1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRWlGO0FBQ3BDO0FBQ0Y7QUFRM0MsTUFBTU0sOEJBQWdCTixvREFBYUEsQ0FBb0I7SUFDckRPLFFBQVE7SUFDUkMsYUFBYTtBQUNmO0FBTU8sU0FBU0MsZUFBZSxFQUFFQyxRQUFRLEVBQXVCO0lBQzlELE1BQU0sQ0FBQ0gsUUFBUUksVUFBVSxHQUFHUiwrQ0FBUUEsQ0FBOEI7SUFDbEUsTUFBTSxDQUFDSyxhQUFhSSxlQUFlLEdBQUdULCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sRUFBRVUsSUFBSSxFQUFFQyxlQUFlLEVBQUUsR0FBR1Qsd0RBQU9BO0lBRXpDSCxnREFBU0E7b0NBQUM7WUFDUixJQUFJWSxtQkFBbUJELE1BQU07Z0JBQzNCLCtCQUErQjtnQkFDL0IsTUFBTUUsaUJBQWlCWCxvREFBRUEsQ0FBQ1ksUUFBUUMsR0FBRyxDQUFDQyxzQkFBc0IsSUFBSSx5QkFBeUI7b0JBQ3ZGQyxNQUFNO3dCQUNKQyxRQUFRUCxLQUFLUSxFQUFFO29CQUNqQjtvQkFDQUMsYUFBYTtnQkFDZjtnQkFFQVAsZUFBZVEsRUFBRSxDQUFDO2dEQUFXO3dCQUMzQkMsUUFBUUMsR0FBRyxDQUFDLHFCQUFxQlYsZUFBZU0sRUFBRTt3QkFDbERULGVBQWU7b0JBQ2pCOztnQkFFQUcsZUFBZVEsRUFBRSxDQUFDO2dEQUFjO3dCQUM5QkMsUUFBUUMsR0FBRyxDQUFDO3dCQUNaYixlQUFlO29CQUNqQjs7Z0JBRUFHLGVBQWVRLEVBQUUsQ0FBQztnREFBaUIsQ0FBQ0c7d0JBQ2xDRixRQUFRRSxLQUFLLENBQUMsNEJBQTRCQTt3QkFDMUNkLGVBQWU7b0JBQ2pCOztnQkFFQUQsVUFBVUk7Z0JBRVY7Z0RBQU87d0JBQ0xBLGVBQWVZLFVBQVU7d0JBQ3pCaEIsVUFBVTt3QkFDVkMsZUFBZTtvQkFDakI7O1lBQ0YsT0FBTztnQkFDTCxpREFBaUQ7Z0JBQ2pELElBQUlMLFFBQVE7b0JBQ1ZBLE9BQU9vQixVQUFVO29CQUNqQmhCLFVBQVU7b0JBQ1ZDLGVBQWU7Z0JBQ2pCO1lBQ0Y7UUFDRjttQ0FBRztRQUFDRTtRQUFpQkQsTUFBTVE7S0FBRztJQUU5QixxQkFDRSw4REFBQ2YsY0FBY3NCLFFBQVE7UUFBQ0MsT0FBTztZQUFFdEI7WUFBUUM7UUFBWTtrQkFDbERFOzs7Ozs7QUFHUDtBQUVPLFNBQVNvQjtJQUNkLE1BQU1DLFVBQVU5QixpREFBVUEsQ0FBQ0s7SUFDM0IsSUFBSXlCLFlBQVlDLFdBQVc7UUFDekIsTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Y7QUFDVCIsInNvdXJjZXMiOlsiQzpcXEhhY2thdGhvblxcT2Rvb1xcT2Rvb1xcQ2xpZW50XFxzcmNcXHByb3ZpZGVyc1xcc29ja2V0LXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgeyBpbywgU29ja2V0IH0gZnJvbSAnc29ja2V0LmlvLWNsaWVudCdcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJy4uL2hvb2tzL3VzZS1hdXRoJ1xyXG5pbXBvcnQgdHlwZSB7IFNvY2tldEV2ZW50cyB9IGZyb20gJy4uL3R5cGVzJ1xyXG5cclxuaW50ZXJmYWNlIFNvY2tldENvbnRleHRUeXBlIHtcclxuICBzb2NrZXQ6IFNvY2tldDxTb2NrZXRFdmVudHM+IHwgbnVsbFxyXG4gIGlzQ29ubmVjdGVkOiBib29sZWFuXHJcbn1cclxuXHJcbmNvbnN0IFNvY2tldENvbnRleHQgPSBjcmVhdGVDb250ZXh0PFNvY2tldENvbnRleHRUeXBlPih7XHJcbiAgc29ja2V0OiBudWxsLFxyXG4gIGlzQ29ubmVjdGVkOiBmYWxzZSxcclxufSlcclxuXHJcbmludGVyZmFjZSBTb2NrZXRQcm92aWRlclByb3BzIHtcclxuICBjaGlsZHJlbjogUmVhY3ROb2RlXHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBTb2NrZXRQcm92aWRlcih7IGNoaWxkcmVuIH06IFNvY2tldFByb3ZpZGVyUHJvcHMpIHtcclxuICBjb25zdCBbc29ja2V0LCBzZXRTb2NrZXRdID0gdXNlU3RhdGU8U29ja2V0PFNvY2tldEV2ZW50cz4gfCBudWxsPihudWxsKVxyXG4gIGNvbnN0IFtpc0Nvbm5lY3RlZCwgc2V0SXNDb25uZWN0ZWRdID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgeyB1c2VyLCBpc0F1dGhlbnRpY2F0ZWQgfSA9IHVzZUF1dGgoKVxyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGlzQXV0aGVudGljYXRlZCAmJiB1c2VyKSB7XHJcbiAgICAgIC8vIEluaXRpYWxpemUgc29ja2V0IGNvbm5lY3Rpb25cclxuICAgICAgY29uc3Qgc29ja2V0SW5zdGFuY2UgPSBpbyhwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TT0NLRVRfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDEnLCB7XHJcbiAgICAgICAgYXV0aDoge1xyXG4gICAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYXV0b0Nvbm5lY3Q6IHRydWUsXHJcbiAgICAgIH0pXHJcblxyXG4gICAgICBzb2NrZXRJbnN0YW5jZS5vbignY29ubmVjdCcsICgpID0+IHtcclxuICAgICAgICBjb25zb2xlLmxvZygnU29ja2V0IGNvbm5lY3RlZDonLCBzb2NrZXRJbnN0YW5jZS5pZClcclxuICAgICAgICBzZXRJc0Nvbm5lY3RlZCh0cnVlKVxyXG4gICAgICB9KVxyXG5cclxuICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2Rpc2Nvbm5lY3QnLCAoKSA9PiB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ1NvY2tldCBkaXNjb25uZWN0ZWQnKVxyXG4gICAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKVxyXG4gICAgICB9KVxyXG5cclxuICAgICAgc29ja2V0SW5zdGFuY2Uub24oJ2Nvbm5lY3RfZXJyb3InLCAoZXJyb3IpID0+IHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdTb2NrZXQgY29ubmVjdGlvbiBlcnJvcjonLCBlcnJvcilcclxuICAgICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSlcclxuICAgICAgfSlcclxuXHJcbiAgICAgIHNldFNvY2tldChzb2NrZXRJbnN0YW5jZSlcclxuXHJcbiAgICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgICAgc29ja2V0SW5zdGFuY2UuZGlzY29ubmVjdCgpXHJcbiAgICAgICAgc2V0U29ja2V0KG51bGwpXHJcbiAgICAgICAgc2V0SXNDb25uZWN0ZWQoZmFsc2UpXHJcbiAgICAgIH1cclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIERpc2Nvbm5lY3Qgc29ja2V0IGlmIHVzZXIgaXMgbm90IGF1dGhlbnRpY2F0ZWRcclxuICAgICAgaWYgKHNvY2tldCkge1xyXG4gICAgICAgIHNvY2tldC5kaXNjb25uZWN0KClcclxuICAgICAgICBzZXRTb2NrZXQobnVsbClcclxuICAgICAgICBzZXRJc0Nvbm5lY3RlZChmYWxzZSlcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH0sIFtpc0F1dGhlbnRpY2F0ZWQsIHVzZXI/LmlkXSlcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxTb2NrZXRDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IHNvY2tldCwgaXNDb25uZWN0ZWQgfX0+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvU29ja2V0Q29udGV4dC5Qcm92aWRlcj5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiB1c2VTb2NrZXQoKSB7XHJcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoU29ja2V0Q29udGV4dClcclxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XHJcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVNvY2tldCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgU29ja2V0UHJvdmlkZXInKVxyXG4gIH1cclxuICByZXR1cm4gY29udGV4dFxyXG59XHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiaW8iLCJ1c2VBdXRoIiwiU29ja2V0Q29udGV4dCIsInNvY2tldCIsImlzQ29ubmVjdGVkIiwiU29ja2V0UHJvdmlkZXIiLCJjaGlsZHJlbiIsInNldFNvY2tldCIsInNldElzQ29ubmVjdGVkIiwidXNlciIsImlzQXV0aGVudGljYXRlZCIsInNvY2tldEluc3RhbmNlIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NPQ0tFVF9VUkwiLCJhdXRoIiwidXNlcklkIiwiaWQiLCJhdXRvQ29ubmVjdCIsIm9uIiwiY29uc29sZSIsImxvZyIsImVycm9yIiwiZGlzY29ubmVjdCIsIlByb3ZpZGVyIiwidmFsdWUiLCJ1c2VTb2NrZXQiLCJjb250ZXh0IiwidW5kZWZpbmVkIiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/socket-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/store/auth-store.ts":
/*!*********************************!*\
  !*** ./src/store/auth-store.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        setUser: (user)=>set({\n                user,\n                isAuthenticated: !!user\n            }),\n        setLoading: (isLoading)=>set({\n                isLoading\n            }),\n        login: async (email, password)=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                const response = await fetch('/api/auth/login', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error('Login failed');\n                }\n                const { user } = await response.json();\n                set({\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            // Clear auth state\n            set({\n                user: null,\n                isAuthenticated: false\n            });\n            // Call logout API\n            fetch('/api/auth/logout', {\n                method: 'POST'\n            }).catch(console.error);\n        },\n        updateProfile: async (data)=>{\n            try {\n                const currentUser = get().user;\n                if (!currentUser) throw new Error('No user logged in');\n                set({\n                    isLoading: true\n                });\n                const response = await fetch('/api/user/profile', {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (!response.ok) {\n                    throw new Error('Profile update failed');\n                }\n                const { user } = await response.json();\n                set({\n                    user,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        }\n    }), {\n    name: 'auth-storage',\n    partialize: (state)=>({\n            user: state.user,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3RvcmUvYXV0aC1zdG9yZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFDWTtBQVdyQyxNQUFNRSxlQUFlRiwrQ0FBTUEsR0FDaENDLDJEQUFPQSxDQUNMLENBQUNFLEtBQUtDLE1BQVM7UUFDYkMsTUFBTTtRQUNOQyxpQkFBaUI7UUFDakJDLFdBQVc7UUFFWEMsU0FBUyxDQUFDSCxPQUFTRixJQUFJO2dCQUFFRTtnQkFBTUMsaUJBQWlCLENBQUMsQ0FBQ0Q7WUFBSztRQUN2REksWUFBWSxDQUFDRixZQUFjSixJQUFJO2dCQUFFSTtZQUFVO1FBRTNDRyxPQUFPLE9BQU9DLE9BQWVDO1lBQzNCLElBQUk7Z0JBQ0ZULElBQUk7b0JBQUVJLFdBQVc7Z0JBQUs7Z0JBRXRCLE1BQU1NLFdBQVcsTUFBTUMsTUFBTSxtQkFBbUI7b0JBQzlDQyxRQUFRO29CQUNSQyxTQUFTO3dCQUNQLGdCQUFnQjtvQkFDbEI7b0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRVI7d0JBQU9DO29CQUFTO2dCQUN6QztnQkFFQSxJQUFJLENBQUNDLFNBQVNPLEVBQUUsRUFBRTtvQkFDaEIsTUFBTSxJQUFJQyxNQUFNO2dCQUNsQjtnQkFFQSxNQUFNLEVBQUVoQixJQUFJLEVBQUUsR0FBRyxNQUFNUSxTQUFTUyxJQUFJO2dCQUNwQ25CLElBQUk7b0JBQUVFO29CQUFNQyxpQkFBaUI7b0JBQU1DLFdBQVc7Z0JBQU07WUFDdEQsRUFBRSxPQUFPZ0IsT0FBTztnQkFDZHBCLElBQUk7b0JBQUVJLFdBQVc7Z0JBQU07Z0JBQ3ZCLE1BQU1nQjtZQUNSO1FBQ0Y7UUFFQUMsUUFBUTtZQUNOLG1CQUFtQjtZQUNuQnJCLElBQUk7Z0JBQUVFLE1BQU07Z0JBQU1DLGlCQUFpQjtZQUFNO1lBRXpDLGtCQUFrQjtZQUNsQlEsTUFBTSxvQkFBb0I7Z0JBQUVDLFFBQVE7WUFBTyxHQUN4Q1UsS0FBSyxDQUFDQyxRQUFRSCxLQUFLO1FBQ3hCO1FBRUFJLGVBQWUsT0FBT0M7WUFDcEIsSUFBSTtnQkFDRixNQUFNQyxjQUFjekIsTUFBTUMsSUFBSTtnQkFDOUIsSUFBSSxDQUFDd0IsYUFBYSxNQUFNLElBQUlSLE1BQU07Z0JBRWxDbEIsSUFBSTtvQkFBRUksV0FBVztnQkFBSztnQkFFdEIsTUFBTU0sV0FBVyxNQUFNQyxNQUFNLHFCQUFxQjtvQkFDaERDLFFBQVE7b0JBQ1JDLFNBQVM7d0JBQ1AsZ0JBQWdCO29CQUNsQjtvQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDUztnQkFDdkI7Z0JBRUEsSUFBSSxDQUFDZixTQUFTTyxFQUFFLEVBQUU7b0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtnQkFDbEI7Z0JBRUEsTUFBTSxFQUFFaEIsSUFBSSxFQUFFLEdBQUcsTUFBTVEsU0FBU1MsSUFBSTtnQkFDcENuQixJQUFJO29CQUFFRTtvQkFBTUUsV0FBVztnQkFBTTtZQUMvQixFQUFFLE9BQU9nQixPQUFPO2dCQUNkcEIsSUFBSTtvQkFBRUksV0FBVztnQkFBTTtnQkFDdkIsTUFBTWdCO1lBQ1I7UUFDRjtJQUNGLElBQ0E7SUFDRU8sTUFBTTtJQUNOQyxZQUFZLENBQUNDLFFBQVc7WUFDdEIzQixNQUFNMkIsTUFBTTNCLElBQUk7WUFDaEJDLGlCQUFpQjBCLE1BQU0xQixlQUFlO1FBQ3hDO0FBQ0YsSUFFSCIsInNvdXJjZXMiOlsiQzpcXEhhY2thdGhvblxcT2Rvb1xcT2Rvb1xcQ2xpZW50XFxzcmNcXHN0b3JlXFxhdXRoLXN0b3JlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZSB9IGZyb20gJ3p1c3RhbmQnXHJcbmltcG9ydCB7IHBlcnNpc3QgfSBmcm9tICd6dXN0YW5kL21pZGRsZXdhcmUnXHJcbmltcG9ydCB0eXBlIHsgQXV0aFN0YXRlLCBVc2VyIH0gZnJvbSAnLi4vdHlwZXMnXHJcblxyXG5pbnRlcmZhY2UgQXV0aFN0b3JlIGV4dGVuZHMgQXV0aFN0YXRlIHtcclxuICBzZXRVc2VyOiAodXNlcjogVXNlciB8IG51bGwpID0+IHZvaWRcclxuICBzZXRMb2FkaW5nOiAobG9hZGluZzogYm9vbGVhbikgPT4gdm9pZFxyXG4gIGxvZ2luOiAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPlxyXG4gIGxvZ291dDogKCkgPT4gdm9pZFxyXG4gIHVwZGF0ZVByb2ZpbGU6IChkYXRhOiBQYXJ0aWFsPFVzZXI+KSA9PiBQcm9taXNlPHZvaWQ+XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCB1c2VBdXRoU3RvcmUgPSBjcmVhdGU8QXV0aFN0b3JlPigpKFxyXG4gIHBlcnNpc3QoXHJcbiAgICAoc2V0LCBnZXQpID0+ICh7XHJcbiAgICAgIHVzZXI6IG51bGwsXHJcbiAgICAgIGlzQXV0aGVudGljYXRlZDogZmFsc2UsXHJcbiAgICAgIGlzTG9hZGluZzogZmFsc2UsXHJcblxyXG4gICAgICBzZXRVc2VyOiAodXNlcikgPT4gc2V0KHsgdXNlciwgaXNBdXRoZW50aWNhdGVkOiAhIXVzZXIgfSksXHJcbiAgICAgIHNldExvYWRpbmc6IChpc0xvYWRpbmcpID0+IHNldCh7IGlzTG9hZGluZyB9KSxcclxuXHJcbiAgICAgIGxvZ2luOiBhc3luYyAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4ge1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IHRydWUgfSlcclxuICAgICAgICAgIFxyXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL2xvZ2luJywge1xyXG4gICAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcclxuICAgICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZW1haWwsIHBhc3N3b3JkIH0pLFxyXG4gICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignTG9naW4gZmFpbGVkJylcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBjb25zdCB7IHVzZXIgfSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxyXG4gICAgICAgICAgc2V0KHsgdXNlciwgaXNBdXRoZW50aWNhdGVkOiB0cnVlLCBpc0xvYWRpbmc6IGZhbHNlIH0pXHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIHNldCh7IGlzTG9hZGluZzogZmFsc2UgfSlcclxuICAgICAgICAgIHRocm93IGVycm9yXHJcbiAgICAgICAgfVxyXG4gICAgICB9LFxyXG5cclxuICAgICAgbG9nb3V0OiAoKSA9PiB7XHJcbiAgICAgICAgLy8gQ2xlYXIgYXV0aCBzdGF0ZVxyXG4gICAgICAgIHNldCh7IHVzZXI6IG51bGwsIGlzQXV0aGVudGljYXRlZDogZmFsc2UgfSlcclxuICAgICAgICBcclxuICAgICAgICAvLyBDYWxsIGxvZ291dCBBUElcclxuICAgICAgICBmZXRjaCgnL2FwaS9hdXRoL2xvZ291dCcsIHsgbWV0aG9kOiAnUE9TVCcgfSlcclxuICAgICAgICAgIC5jYXRjaChjb25zb2xlLmVycm9yKVxyXG4gICAgICB9LFxyXG5cclxuICAgICAgdXBkYXRlUHJvZmlsZTogYXN5bmMgKGRhdGE6IFBhcnRpYWw8VXNlcj4pID0+IHtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgY29uc3QgY3VycmVudFVzZXIgPSBnZXQoKS51c2VyXHJcbiAgICAgICAgICBpZiAoIWN1cnJlbnRVc2VyKSB0aHJvdyBuZXcgRXJyb3IoJ05vIHVzZXIgbG9nZ2VkIGluJylcclxuXHJcbiAgICAgICAgICBzZXQoeyBpc0xvYWRpbmc6IHRydWUgfSlcclxuXHJcbiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3VzZXIvcHJvZmlsZScsIHtcclxuICAgICAgICAgICAgbWV0aG9kOiAnUEFUQ0gnLFxyXG4gICAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoZGF0YSksXHJcbiAgICAgICAgICB9KVxyXG5cclxuICAgICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdQcm9maWxlIHVwZGF0ZSBmYWlsZWQnKVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGNvbnN0IHsgdXNlciB9ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXHJcbiAgICAgICAgICBzZXQoeyB1c2VyLCBpc0xvYWRpbmc6IGZhbHNlIH0pXHJcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgIHNldCh7IGlzTG9hZGluZzogZmFsc2UgfSlcclxuICAgICAgICAgIHRocm93IGVycm9yXHJcbiAgICAgICAgfVxyXG4gICAgICB9LFxyXG4gICAgfSksXHJcbiAgICB7XHJcbiAgICAgIG5hbWU6ICdhdXRoLXN0b3JhZ2UnLFxyXG4gICAgICBwYXJ0aWFsaXplOiAoc3RhdGUpID0+ICh7XHJcbiAgICAgICAgdXNlcjogc3RhdGUudXNlcixcclxuICAgICAgICBpc0F1dGhlbnRpY2F0ZWQ6IHN0YXRlLmlzQXV0aGVudGljYXRlZCxcclxuICAgICAgfSksXHJcbiAgICB9XHJcbiAgKVxyXG4pXHJcbiJdLCJuYW1lcyI6WyJjcmVhdGUiLCJwZXJzaXN0IiwidXNlQXV0aFN0b3JlIiwic2V0IiwiZ2V0IiwidXNlciIsImlzQXV0aGVudGljYXRlZCIsImlzTG9hZGluZyIsInNldFVzZXIiLCJzZXRMb2FkaW5nIiwibG9naW4iLCJlbWFpbCIsInBhc3N3b3JkIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm9rIiwiRXJyb3IiLCJqc29uIiwiZXJyb3IiLCJsb2dvdXQiLCJjYXRjaCIsImNvbnNvbGUiLCJ1cGRhdGVQcm9maWxlIiwiZGF0YSIsImN1cnJlbnRVc2VyIiwibmFtZSIsInBhcnRpYWxpemUiLCJzdGF0ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/store/auth-store.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@heroicons","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/zustand","vendor-chunks/motion-utils","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();