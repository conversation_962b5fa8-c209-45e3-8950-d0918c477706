globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(ssr)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/auth-provider.tsx":{"*":{"id":"(ssr)/./src/providers/auth-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/socket-provider.tsx":{"*":{"id":"(ssr)/./src/providers/socket-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/cta-section.tsx":{"*":{"id":"(ssr)/./src/components/landing/cta-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/features-section.tsx":{"*":{"id":"(ssr)/./src/components/landing/features-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/hero-section.tsx":{"*":{"id":"(ssr)/./src/components/landing/hero-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/how-it-works-section.tsx":{"*":{"id":"(ssr)/./src/components/landing/how-it-works-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/testimonials-section.tsx":{"*":{"id":"(ssr)/./src/components/landing/testimonials-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/header.tsx":{"*":{"id":"(ssr)/./src/components/layout/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/loading.tsx":{"*":{"id":"(ssr)/./src/components/ui/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/mobile-prototype/page.tsx":{"*":{"id":"(ssr)/./src/app/mobile-prototype/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./src/components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\auth-provider.tsx":{"id":"(app-pages-browser)/./src/providers/auth-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\socket-provider.tsx":{"id":"(app-pages-browser)/./src/providers/socket-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\landing\\cta-section.tsx":{"id":"(app-pages-browser)/./src/components/landing/cta-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\landing\\features-section.tsx":{"id":"(app-pages-browser)/./src/components/landing/features-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\landing\\hero-section.tsx":{"id":"(app-pages-browser)/./src/components/landing/hero-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\landing\\how-it-works-section.tsx":{"id":"(app-pages-browser)/./src/components/landing/how-it-works-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\landing\\testimonials-section.tsx":{"id":"(app-pages-browser)/./src/components/landing/testimonials-section.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\layout\\header.tsx":{"id":"(app-pages-browser)/./src/components/layout/header.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\ui\\loading.tsx":{"id":"(app-pages-browser)/./src/components/ui/loading.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\app\\mobile-prototype\\page.tsx":{"id":"(app-pages-browser)/./src/app/mobile-prototype/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\":[],"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(rsc)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/auth-provider.tsx":{"*":{"id":"(rsc)/./src/providers/auth-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/socket-provider.tsx":{"*":{"id":"(rsc)/./src/providers/socket-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/cta-section.tsx":{"*":{"id":"(rsc)/./src/components/landing/cta-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/features-section.tsx":{"*":{"id":"(rsc)/./src/components/landing/features-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/hero-section.tsx":{"*":{"id":"(rsc)/./src/components/landing/hero-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/how-it-works-section.tsx":{"*":{"id":"(rsc)/./src/components/landing/how-it-works-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/testimonials-section.tsx":{"*":{"id":"(rsc)/./src/components/landing/testimonials-section.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/header.tsx":{"*":{"id":"(rsc)/./src/components/layout/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/loading.tsx":{"*":{"id":"(rsc)/./src/components/ui/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/mobile-prototype/page.tsx":{"*":{"id":"(rsc)/./src/app/mobile-prototype/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}