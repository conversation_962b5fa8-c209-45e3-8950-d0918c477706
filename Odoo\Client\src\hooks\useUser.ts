import { useState, useEffect } from 'react';
import { userService } from '../services/userService';
import { User, CreateUserRequest, UpdateUserRequest } from '../types/api';

// Hook for user registration
export const useRegister = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const register = async (userData: CreateUserRequest): Promise<User | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await userService.register(userData);
      return response.user || null;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { register, loading, error };
};

// Hook for fetching user profile
export const useUserProfile = (userId: number | null) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (userId) {
      fetchUser();
    }
  }, [userId]);

  const fetchUser = async () => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await userService.getProfile(userId);
      setUser(response.user || null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { user, loading, error, refetch: fetchUser };
};

// Hook for updating user profile
export const useUpdateProfile = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateProfile = async (userId: number, userData: UpdateUserRequest): Promise<User | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await userService.updateProfile(userId, userData);
      return response.user || null;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { updateProfile, loading, error };
};

// Hook for fetching public users
export const usePublicUsers = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await userService.getPublicUsers();
      setUsers(response.users || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { users, loading, error, refetch: fetchUsers };
};

// Hook for uploading avatar
export const useUploadAvatar = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadAvatar = async (userId: number, file: File): Promise<string | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await userService.uploadAvatar(userId, file);
      return response.avatarUrl || null;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { uploadAvatar, loading, error };
};
