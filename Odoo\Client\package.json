{"name": "skill-swap-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.50.5", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.6.0", "framer-motion": "^12.23.3", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.2"}}