// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getStorage } from "firebase/storage";
import { getAnalytics } from "firebase/analytics";
import dotenv from 'dotenv';
import path from 'path';

// Load .env from the parent directory (Server folder)
dotenv.config({ path: path.resolve(process.cwd(), '..', '.env') });

const { FIREBASE_API_KEY } = process.env;
const firebaseConfig = {
  apiKey: FIREBASE_API_KEY,
  authDomain: "odoo-c9ed7.firebaseapp.com",
  projectId: "odoo-c9ed7",
  storageBucket: "odoo-c9ed7.firebasestorage.app",
  messagingSenderId: "919882015112",
  appId: "1:919882015112:web:3fdfa66ecda874aa631a3d",
  measurementId: "G-0V0373843H"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const storage = getStorage(app);
export { storage };