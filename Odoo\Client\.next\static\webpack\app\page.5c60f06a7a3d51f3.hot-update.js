"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/section.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/section.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Section: () => (/* binding */ Section),\n/* harmony export */   SectionGrid: () => (/* binding */ SectionGrid),\n/* harmony export */   SectionHeader: () => (/* binding */ SectionHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _animated_section__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./animated-section */ \"(app-pages-browser)/./src/components/ui/animated-section.tsx\");\n/* __next_internal_client_entry_do_not_use__ Section,SectionHeader,SectionGrid auto */ \n\n\n\nconst backgroundVariants = {\n    white: 'bg-white',\n    gray: 'bg-gray-50',\n    gradient: 'bg-gradient-to-br from-blue-50 via-white to-purple-50',\n    warm: 'bg-warm-200'\n};\nconst paddingVariants = {\n    sm: 'py-12',\n    md: 'py-16',\n    lg: 'py-20',\n    xl: 'py-24'\n};\nconst maxWidthVariants = {\n    sm: 'max-w-sm',\n    md: 'max-w-md',\n    lg: 'max-w-lg',\n    xl: 'max-w-xl',\n    '2xl': 'max-w-2xl',\n    '4xl': 'max-w-4xl',\n    '6xl': 'max-w-6xl',\n    '7xl': 'max-w-7xl',\n    full: 'max-w-full'\n};\nfunction Section(param) {\n    let { children, className, containerClassName, background = 'white', padding = 'lg', animated = true, maxWidth = '7xl' } = param;\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(backgroundVariants[background], paddingVariants[padding], className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('mx-auto px-6 lg:px-8', maxWidthVariants[maxWidth], containerClassName),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n    if (animated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedSection, {\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    return content;\n}\n_c = Section;\nfunction SectionHeader(param) {\n    let { title, subtitle, description, centered = true, className, animated = true } = param;\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(centered ? 'text-center' : 'text-left', 'mb-16', className),\n        children: [\n            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm font-semibold text-primary-600 uppercase tracking-wide mb-3\",\n                children: subtitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n    if (animated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedSection, {\n            delay: 0.1,\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    return content;\n}\n_c1 = SectionHeader;\nfunction SectionGrid(param) {\n    let { children, columns = 3, gap = 'lg', className } = param;\n    const columnVariants = {\n        1: 'grid-cols-1',\n        2: 'grid-cols-1 md:grid-cols-2',\n        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n        4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'\n    };\n    const gapVariants = {\n        sm: 'gap-6',\n        md: 'gap-8',\n        lg: 'gap-12'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('grid', columnVariants[columns], gapVariants[gap], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_c2 = SectionGrid;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Section\");\n$RefreshReg$(_c1, \"SectionHeader\");\n$RefreshReg$(_c2, \"SectionGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/section.tsx\n"));

/***/ })

});