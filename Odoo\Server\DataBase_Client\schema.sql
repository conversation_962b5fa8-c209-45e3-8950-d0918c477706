-- Skill Swap Platform Database Schema
-- Drop tables if they exist (in correct order due to foreign keys)
DROP TABLE IF EXISTS skills_wanted;
DROP TABLE IF EXISTS skills_offered;
DROP TABLE IF EXISTS users;

-- Create users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(100),
    avatar VARCHAR(255) DEFAULT NULL,
    privacy VARCHAR(20) DEFAULT 'public' CHECK (privacy IN ('public', 'private')),
    availability VARCHAR(50) DEFAULT 'available' CHECK (availability IN ('available', 'busy', 'offline')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create skills_offered table
CREATE TABLE skills_offered (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    skill_name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create skills_wanted table
CREATE TABLE skills_wanted (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    skill_name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_skills_offered_user_id ON skills_offered(user_id);
CREATE INDEX idx_skills_offered_skill_name ON skills_offered(skill_name);
CREATE INDEX idx_skills_offered_category ON skills_offered(category);
CREATE INDEX idx_skills_wanted_user_id ON skills_wanted(user_id);
CREATE INDEX idx_skills_wanted_skill_name ON skills_wanted(skill_name);
CREATE INDEX idx_skills_wanted_category ON skills_wanted(category);

-- Insert sample data for testing
INSERT INTO users (email, password, name, location, privacy, availability) VALUES
('<EMAIL>', 'hashed_password_1', 'John Doe', 'New York', 'public', 'available'),
('<EMAIL>', 'hashed_password_2', 'Jane Smith', 'San Francisco', 'public', 'available'),
('<EMAIL>', 'hashed_password_3', 'Bob Johnson', 'Chicago', 'private', 'busy');

INSERT INTO skills_offered (user_id, skill_name, description, category) VALUES
(1, 'JavaScript', 'Full-stack JavaScript development with React and Node.js', 'Programming'),
(1, 'Guitar', 'Acoustic guitar lessons for beginners', 'Music'),
(2, 'Python', 'Data science and machine learning with Python', 'Programming'),
(2, 'Photography', 'Portrait and landscape photography', 'Art'),
(3, 'Cooking', 'Italian cuisine and pasta making', 'Culinary');

INSERT INTO skills_wanted (user_id, skill_name, description, category) VALUES
(1, 'Spanish', 'Looking to learn conversational Spanish', 'Language'),
(1, 'Photography', 'Want to learn basic photography techniques', 'Art'),
(2, 'Guitar', 'Interested in learning guitar basics', 'Music'),
(3, 'Web Development', 'Want to learn modern web development', 'Programming'),
(3, 'German', 'Need to learn German for work', 'Language');
