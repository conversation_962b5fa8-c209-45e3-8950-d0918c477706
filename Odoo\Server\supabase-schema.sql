-- Skill Swap Platform Database Schema for Supabase
-- Run this in your Supabase SQL editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    location VARCHAR(255),
    avatar VARCHAR(500),
    privacy VARCHAR(20) DEFAULT 'public' CHECK (privacy IN ('public', 'private')),
    availability VARCHAR(255) DEFAULT 'available',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Skills offered table
CREATE TABLE IF NOT EXISTS public.skill_offered (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    skill_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'Other',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Skills wanted table
CREATE TABLE IF NOT EXISTS public.skill_wanted (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    skill_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'Other',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Swap requests table
CREATE TABLE IF NOT EXISTS public.swap_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    requester_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    receiver_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    offered_skill_id UUID REFERENCES public.skill_offered(id) ON DELETE CASCADE NOT NULL,
    wanted_skill_id UUID REFERENCES public.skill_wanted(id) ON DELETE CASCADE NOT NULL,
    message TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'completed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_privacy ON public.users(privacy);
CREATE INDEX IF NOT EXISTS idx_skill_offered_user_id ON public.skill_offered(user_id);
CREATE INDEX IF NOT EXISTS idx_skill_offered_category ON public.skill_offered(category);
CREATE INDEX IF NOT EXISTS idx_skill_wanted_user_id ON public.skill_wanted(user_id);
CREATE INDEX IF NOT EXISTS idx_skill_wanted_category ON public.skill_wanted(category);
CREATE INDEX IF NOT EXISTS idx_swap_requests_requester ON public.swap_requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_swap_requests_receiver ON public.swap_requests(receiver_id);
CREATE INDEX IF NOT EXISTS idx_swap_requests_status ON public.swap_requests(status);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.skill_offered ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.skill_wanted ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.swap_requests ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
CREATE POLICY "Users can view public profiles" ON public.users
    FOR SELECT USING (privacy = 'public' OR auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for skill_offered table
CREATE POLICY "Anyone can view offered skills from public users" ON public.skill_offered
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE users.id = skill_offered.user_id
            AND users.privacy = 'public'
        )
    );

CREATE POLICY "Users can manage own offered skills" ON public.skill_offered
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for skill_wanted table
CREATE POLICY "Anyone can view wanted skills from public users" ON public.skill_wanted
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE users.id = skill_wanted.user_id
            AND users.privacy = 'public'
        )
    );

CREATE POLICY "Users can manage own wanted skills" ON public.skill_wanted
    FOR ALL USING (auth.uid() = user_id);

-- RLS Policies for swap_requests table
CREATE POLICY "Users can view their own swap requests" ON public.swap_requests
    FOR SELECT USING (auth.uid() = requester_id OR auth.uid() = receiver_id);

CREATE POLICY "Users can create swap requests" ON public.swap_requests
    FOR INSERT WITH CHECK (auth.uid() = requester_id);

CREATE POLICY "Receivers can update swap status" ON public.swap_requests
    FOR UPDATE USING (auth.uid() = receiver_id);

-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name, location, privacy, availability)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'User'),
        COALESCE(NEW.raw_user_meta_data->>'location', ''),
        'public',
        'available'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on auth.users insert
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to update updated_at timestamp
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_skill_offered_updated_at
    BEFORE UPDATE ON public.skill_offered
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_skill_wanted_updated_at
    BEFORE UPDATE ON public.skill_wanted
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_swap_requests_updated_at
    BEFORE UPDATE ON public.swap_requests
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Insert some sample data (optional)
-- Note: This will only work after you have actual users in auth.users
-- You can run this manually after creating some test accounts

/*
-- Sample categories for skills
INSERT INTO public.skills_offered (user_id, skill_name, description, category) VALUES
-- Replace these UUIDs with actual user IDs from your auth.users table
-- ('user-uuid-1', 'React Development', 'Frontend development with React and TypeScript', 'Programming'),
-- ('user-uuid-1', 'UI/UX Design', 'User interface and experience design', 'Design'),
-- ('user-uuid-2', 'Python Programming', 'Backend development with Python and Django', 'Programming'),
-- ('user-uuid-2', 'Digital Marketing', 'SEO, social media marketing, and content strategy', 'Marketing');

INSERT INTO public.skills_wanted (user_id, skill_name, description, category) VALUES
-- ('user-uuid-1', 'Spanish Language', 'Conversational Spanish for travel and business', 'Languages'),
-- ('user-uuid-2', 'Guitar Playing', 'Acoustic guitar for beginners', 'Music');
*/
