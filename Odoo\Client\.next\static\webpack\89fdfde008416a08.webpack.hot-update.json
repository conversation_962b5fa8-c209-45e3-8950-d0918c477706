{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js", "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Ccta-section.tsx%22%2C%22ids%22%3A%5B%22CTASection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Cfeatures-section.tsx%22%2C%22ids%22%3A%5B%22FeaturesSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Chow-it-works-section.tsx%22%2C%22ids%22%3A%5B%22HowItWorksSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Ctestimonials-section.tsx%22%2C%22ids%22%3A%5B%22TestimonialsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%22Loading%22%5D%7D&server=false!", "(app-pages-browser)/./src/components/landing/features-section.tsx"]}