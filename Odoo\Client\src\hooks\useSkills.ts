import { useState, useEffect } from 'react';
import { skillService } from '../services/skillService';
import { Skill, CreateSkillRequest } from '../types/api';

// Hook for managing user's offered skills
export const useUserOfferedSkills = (userId: number | null) => {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (userId) {
      fetchSkills();
    }
  }, [userId]);

  const fetchSkills = async () => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await skillService.getUserOfferedSkills(userId);
      setSkills(response.skills || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { skills, loading, error, refetch: fetchSkills };
};

// Hook for managing user's wanted skills
export const useUserWantedSkills = (userId: number | null) => {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (userId) {
      fetchSkills();
    }
  }, [userId]);

  const fetchSkills = async () => {
    if (!userId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await skillService.getUserWantedSkills(userId);
      setSkills(response.skills || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { skills, loading, error, refetch: fetchSkills };
};

// Hook for fetching all offered skills
export const useAllOfferedSkills = () => {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSkills();
  }, []);

  const fetchSkills = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await skillService.getAllOfferedSkills();
      setSkills(response.skills || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { skills, loading, error, refetch: fetchSkills };
};

// Hook for adding skills
export const useAddSkill = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const addOfferedSkill = async (userId: number, skillData: CreateSkillRequest): Promise<Skill | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await skillService.addOfferedSkill(userId, skillData);
      return response.skill || null;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const addWantedSkill = async (userId: number, skillData: CreateSkillRequest): Promise<Skill | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await skillService.addWantedSkill(userId, skillData);
      return response.skill || null;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { addOfferedSkill, addWantedSkill, loading, error };
};

// Hook for searching skills
export const useSearchSkills = () => {
  const [skills, setSkills] = useState<Skill[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchSkills = async (query: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await skillService.searchOfferedSkills(query);
      setSkills(response.skills || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { skills, loading, error, searchSkills };
};

// Hook for deleting skills
export const useDeleteSkill = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteOfferedSkill = async (skillId: number, userId: number): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      await skillService.deleteOfferedSkill(skillId, userId);
      return true;
    } catch (err: any) {
      setError(err.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const deleteWantedSkill = async (skillId: number, userId: number): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      await skillService.deleteWantedSkill(skillId, userId);
      return true;
    } catch (err: any) {
      setError(err.message);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { deleteOfferedSkill, deleteWantedSkill, loading, error };
};
