// User types
export interface User {
  id: number;
  email: string;
  name: string;
  location?: string;
  avatar?: string;
  privacy: 'public' | 'private';
  availability: 'available' | 'busy' | 'offline';
  created_at: string;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  name: string;
  location?: string;
  privacy?: 'public' | 'private';
  availability?: 'available' | 'busy' | 'offline';
}

export interface UpdateUserRequest {
  name?: string;
  location?: string;
  privacy?: 'public' | 'private';
  availability?: 'available' | 'busy' | 'offline';
}

// Skill types
export interface Skill {
  id: number;
  user_id: number;
  skill_name: string;
  description?: string;
  category?: string;
  created_at: string;
  // For joined queries
  user_name?: string;
  user_location?: string;
  user_avatar?: string;
}

export interface CreateSkillRequest {
  skill_name: string;
  description?: string;
  category?: string;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface UserResponse extends ApiResponse<User> {
  user?: User;
}

export interface UsersResponse extends ApiResponse<User[]> {
  users?: User[];
}

export interface SkillResponse extends ApiResponse<Skill> {
  skill?: Skill;
}

export interface SkillsResponse extends ApiResponse<Skill[]> {
  skills?: Skill[];
}

export interface UploadResponse extends ApiResponse<string> {
  avatarUrl?: string;
  user?: User;
}

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message?: string;
  token?: string;
  user?: User;
}
