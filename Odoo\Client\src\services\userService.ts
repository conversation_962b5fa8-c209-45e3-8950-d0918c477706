import api from '../lib/api';
import { 
  User, 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserResponse, 
  UsersResponse, 
  UploadResponse 
} from '../types/api';

// User API service
export const userService = {
  // Register a new user
  register: async (userData: CreateUserRequest): Promise<UserResponse> => {
    try {
      const response = await api.post('/users/register', userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  },

  // Get user profile
  getProfile: async (userId: number): Promise<UserResponse> => {
    try {
      const response = await api.get(`/users/profile/${userId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch profile');
    }
  },

  // Update user profile
  updateProfile: async (userId: number, userData: UpdateUserRequest): Promise<UserResponse> => {
    try {
      const response = await api.put(`/users/profile/${userId}`, userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to update profile');
    }
  },

  // Get all public users
  getPublicUsers: async (): Promise<UsersResponse> => {
    try {
      const response = await api.get('/users/public');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch users');
    }
  },

  // Upload avatar
  uploadAvatar: async (userId: number, file: File): Promise<UploadResponse> => {
    try {
      const formData = new FormData();
      formData.append('avatar', file);
      
      const response = await api.post(`/users/upload-avatar/${userId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to upload avatar');
    }
  },
};
