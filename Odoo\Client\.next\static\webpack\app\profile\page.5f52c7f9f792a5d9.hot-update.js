"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/app/profile/page.tsx":
/*!**********************************!*\
  !*** ./src/app/profile/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProfilePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckIcon,ClockIcon,GlobeAltIcon,MapPinIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CameraIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckIcon,ClockIcon,GlobeAltIcon,MapPinIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckIcon,ClockIcon,GlobeAltIcon,MapPinIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckIcon,ClockIcon,GlobeAltIcon,MapPinIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckIcon,ClockIcon,GlobeAltIcon,MapPinIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckIcon,ClockIcon,GlobeAltIcon,MapPinIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CameraIcon,CheckIcon,ClockIcon,GlobeAltIcon,MapPinIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ProfilePage() {\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        location: \"\",\n        bio: \"\",\n        availability: \"\",\n        isPublic: true\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const mockUser = {\n            id: \"1\",\n            name: \"Alex Johnson\",\n            email: \"<EMAIL>\",\n            location: \"San Francisco, CA\",\n            bio: \"Passionate developer and designer with 5+ years of experience. Love teaching and learning new technologies. Always excited to share knowledge and connect with fellow creators.\",\n            avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=faces\",\n            availability: \"Available weekends\",\n            isPublic: true,\n            joinedDate: \"2024-01-15\"\n        };\n        setUser(mockUser);\n        setEditForm({\n            name: mockUser.name,\n            location: mockUser.location,\n            bio: mockUser.bio,\n            availability: mockUser.availability,\n            isPublic: mockUser.isPublic\n        });\n        setLoading(false);\n    }, []);\n    const handleSave = ()=>{\n        if (user) {\n            setUser({\n                ...user,\n                ...editForm\n            });\n        }\n        setIsEditing(false);\n    };\n    const handleCancel = ()=>{\n        if (user) {\n            setEditForm({\n                name: user.name,\n                location: user.location,\n                bio: user.bio,\n                availability: user.availability,\n                isPublic: user.isPublic\n            });\n        }\n        setIsEditing(false);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-6 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-32 bg-gradient-to-r from-primary-500 to-secondary-500 relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"absolute top-4 right-4 p-2 bg-black bg-opacity-20 rounded-full text-white hover:bg-opacity-30 transition-colors\",\n                            title: \"Change cover photo\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 pb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start -mt-16 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-24 w-24 rounded-full border-4 border-white bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center\",\n                                            children: (user === null || user === void 0 ? void 0 : user.avatar) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: user.avatar,\n                                                alt: user.name,\n                                                className: \"h-24 w-24 rounded-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: user === null || user === void 0 ? void 0 : user.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"absolute bottom-0 right-0 p-1.5 bg-white rounded-full shadow-md border border-gray-200 hover:bg-gray-50 transition-colors\",\n                                                        title: \"Change profile photo\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            className: \"h-3 w-3 text-gray-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-6 flex-1 mt-16\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: editForm.name,\n                                                                    onChange: (e)=>setEditForm({\n                                                                            ...editForm,\n                                                                            name: e.target.value\n                                                                        }),\n                                                                    className: \"input text-2xl font-bold\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 23\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                                    children: user === null || user === void 0 ? void 0 : user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 148,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: user === null || user === void 0 ? void 0 : user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleSave,\n                                                                        className: \"btn-primary flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 160,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Save\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleCancel,\n                                                                        className: \"btn-secondary flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                                lineNumber: 167,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Cancel\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 163,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setIsEditing(true),\n                                                                className: \"btn-secondary flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                        lineNumber: 176,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Edit Profile\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: editForm.location,\n                                                                    onChange: (e)=>setEditForm({\n                                                                            ...editForm,\n                                                                            location: e.target.value\n                                                                        }),\n                                                                    className: \"input flex-1\",\n                                                                    placeholder: \"Your location\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 23\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: user === null || user === void 0 ? void 0 : user.location\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: editForm.availability,\n                                                                    onChange: (e)=>setEditForm({\n                                                                            ...editForm,\n                                                                            availability: e.target.value\n                                                                        }),\n                                                                    className: \"input flex-1\",\n                                                                    placeholder: \"Your availability\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: user === null || user === void 0 ? void 0 : user.availability\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CameraIcon_CheckIcon_ClockIcon_GlobeAltIcon_MapPinIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"Joined \",\n                                                                        new Date((user === null || user === void 0 ? void 0 : user.joinedDate) || \"\").toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-gray-200 pt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: editForm.bio,\n                                            onChange: (e)=>setEditForm({\n                                                    ...editForm,\n                                                    bio: e.target.value\n                                                }),\n                                            className: \"input w-full h-24 resize-none\",\n                                            placeholder: \"Tell others about yourself...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 leading-relaxed\",\n                                            children: (user === null || user === void 0 ? void 0 : user.bio) || \"No bio added yet.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: \"Privacy Settings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Profile Visibility\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Control who can see your profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setEditForm({\n                                                            ...editForm,\n                                                            isPublic: !editForm.isPublic\n                                                        }),\n                                                    className: \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors \".concat(editForm.isPublic ? \"bg-primary-600\" : \"bg-gray-200\"),\n                                                    title: editForm.isPublic ? \"Make profile private\" : \"Make profile public\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(editForm.isPublic ? \"translate-x-6\" : \"translate-x-1\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Show Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Display your email on your profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200\",\n                                                    title: \"Toggle show email\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white translate-x-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: \"Show Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: \"Display your location on your profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"relative inline-flex h-6 w-11 items-center rounded-full bg-primary-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block h-4 w-4 transform rounded-full bg-white translate-x-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                children: \"Account Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full text-left p-3 border border-gray-200 rounded-lg hover:border-primary-300 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: \"Change Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Update your account password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full text-left p-3 border border-gray-200 rounded-lg hover:border-primary-300 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: \"Notification Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Manage your email and push notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full text-left p-3 border border-gray-200 rounded-lg hover:border-primary-300 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: \"Connected Accounts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Link your social media accounts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full text-left p-3 border border-red-200 rounded-lg hover:border-red-300 transition-colors text-red-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium\",\n                                                children: \"Delete Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: \"Permanently delete your account and data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\profile\\\\page.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfilePage, \"NJ/5kV5S2ScqYqU6i9+Gr7/x8Pw=\");\n_c = ProfilePage;\nvar _c;\n$RefreshReg$(_c, \"ProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/profile/page.tsx\n"));

/***/ })

});