{"name": "server", "version": "1.0.0", "description": "", "main": "server.js", "type": "module", "dependencies": {"@supabase/supabase-js": "^2.50.5", "accepts": "^2.0.0", "body-parser": "^2.2.0", "bytes": "^3.1.2", "call-bind-apply-helpers": "^1.0.2", "call-bound": "^1.0.4", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.2", "cookie-signature": "^1.2.2", "cors": "^2.8.5", "debug": "^4.4.1", "depd": "^2.0.0", "dotenv": "^16.4.5", "dunder-proto": "^1.0.1", "ee-first": "^1.1.1", "encodeurl": "^2.0.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "escape-html": "^1.0.3", "etag": "^1.8.1", "express": "^5.1.0", "finalhandler": "^2.1.0", "forwarded": "^0.2.0", "framer-motion": "^12.23.3", "fresh": "^2.0.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "inherits": "^2.0.4", "ipaddr.js": "^1.9.1", "is-promise": "^4.0.0", "math-intrinsics": "^1.1.0", "media-typer": "^1.1.0", "merge-descriptors": "^2.0.0", "mime-db": "^1.54.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "multer": "^2.0.1", "negotiator": "^1.0.0", "object-inspect": "^1.13.4", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.2.0", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "raw-body": "^3.0.0", "router": "^2.2.0", "safe-buffer": "^5.2.1", "safer-buffer": "^2.1.2", "send": "^1.2.0", "serve-static": "^2.2.0", "setprototypeof": "^1.2.0", "side-channel": "^1.1.0", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2", "statuses": "^2.0.2", "toidentifier": "^1.0.1", "type-is": "^2.0.1", "unpipe": "^1.0.0", "vary": "^1.1.2", "wrappy": "^1.0.2"}, "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "db:setup": "psql -d skillswap_db -f database/schema.sql", "db:reset": "psql -d skillswap_db -c 'DROP SCHEMA public CASCADE; CREATE SCHEMA public;' && npm run db:setup"}, "keywords": [], "author": "", "license": "ISC"}