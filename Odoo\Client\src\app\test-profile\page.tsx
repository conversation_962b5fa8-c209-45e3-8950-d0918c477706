'use client'

import { useState, useEffect } from 'react'
import { authHelpers } from '../../lib/supabase'

export default function TestProfilePage() {
  const [user, setUser] = useState<any>(null)
  const [sessionData, setSessionData] = useState<any>(null)
  const [loginResult, setLoginResult] = useState<any>(null)

  const testLogin = async () => {
    try {
      console.log('Testing login...')
      const result = await authHelpers.signIn('<EMAIL>', 'testpass123')
      console.log('Login result:', result)
      setLoginResult(result)
      
      if (result.data && result.data.user) {
        setUser(result.data.user)
        
        // Test session retrieval
        const session = await authHelpers.getSession()
        console.log('Session after login:', session)
        setSessionData(session)
      }
    } catch (error) {
      console.error('Login test error:', error)
    }
  }

  const testSession = async () => {
    try {
      const session = await authHelpers.getSession()
      console.log('Current session:', session)
      setSessionData(session)
      
      if (session.session && session.session.user) {
        setUser(session.session.user)
      }
    } catch (error) {
      console.error('Session test error:', error)
    }
  }

  const clearSession = () => {
    localStorage.removeItem('user')
    setUser(null)
    setSessionData(null)
    setLoginResult(null)
  }

  useEffect(() => {
    testSession()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Profile Testing</h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Controls */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-800">Controls</h2>
              
              <button
                onClick={testLogin}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
              >
                Test Login
              </button>
              
              <button
                onClick={testSession}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
              >
                Test Session
              </button>
              
              <button
                onClick={clearSession}
                className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700"
              >
                Clear Session
              </button>
            </div>

            {/* Profile Display */}
            <div className="space-y-4">
              <h2 className="text-lg font-semibold text-gray-800">Profile Display</h2>
              
              {user ? (
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <h3 className="text-lg font-medium text-green-900 mb-2">
                    Welcome back, {user.username || 'User'}!
                  </h3>
                  <div className="space-y-2 text-sm">
                    <p><strong>ID:</strong> {user.id}</p>
                    <p><strong>Username:</strong> {user.username}</p>
                    <p><strong>Email:</strong> {user.email}</p>
                    <p><strong>Location:</strong> {user.location || 'Not specified'}</p>
                    <p><strong>Availability:</strong> {user.availability}</p>
                    <p><strong>Avatar:</strong> {user.avatar || 'None'}</p>
                  </div>
                </div>
              ) : (
                <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                  <p className="text-gray-600">No user data available</p>
                </div>
              )}
            </div>
          </div>

          {/* Debug Information */}
          <div className="mt-8 space-y-4">
            <h2 className="text-lg font-semibold text-gray-800">Debug Information</h2>
            
            {loginResult && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 className="font-medium text-blue-900 mb-2">Login Result:</h3>
                <pre className="text-blue-800 text-sm whitespace-pre-wrap">
                  {JSON.stringify(loginResult, null, 2)}
                </pre>
              </div>
            )}
            
            {sessionData && (
              <div className="bg-purple-50 border border-purple-200 rounded-md p-4">
                <h3 className="font-medium text-purple-900 mb-2">Session Data:</h3>
                <pre className="text-purple-800 text-sm whitespace-pre-wrap">
                  {JSON.stringify(sessionData, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
