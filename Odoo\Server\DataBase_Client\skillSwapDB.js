import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import path from 'path';

// Load .env from the current directory
dotenv.config({ path: path.resolve(process.cwd(), '.env') })

const {
    SUPABASE_URL,
    SUPABASE_ANON_KEY,
    SUPABASE_SERVICE_ROLE_KEY,
} = process.env

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
    throw new Error('Missing Supabase environment variables');
}

// Create Supabase client for server-side operations
export const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY || SUPABASE_ANON_KEY, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
});

// Test connection
const testConnection = async () => {
    try {
        const { data, error } = await supabase.from('users').select('count').limit(1);
        if (error) {
            console.error('Supabase connection error:', error.message);
        } else {
            console.log('Connected to Supabase database');
        }
    } catch (err) {
        console.error('Supabase connection test failed:', err.message);
    }
};

testConnection();

// User operations using Supabase
export const userOperations = {
    // Create user profile (auth user is created separately)
    async createUser(userData) {
        const { data, error } = await supabase
            .from('users')
            .insert([userData])
            .select()
            .single();

        if (error) throw error;
        return data;
    },

    // Get user by email
    async getUserByEmail(email) {
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('email', email)
            .single();

        if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
        return data;
    },

    // Get user by ID
    async getUserById(id) {
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', id)
            .single();

        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },

    // Update user profile
    async updateUser(id, updates) {
        const { data, error } = await supabase
            .from('users')
            .update(updates)
            .eq('id', id)
            .select()
            .single();

        if (error) throw error;
        return data;
    },

    // Get all public users
    async getPublicUsers() {
        const { data, error } = await supabase
            .from('users')
            .select('id, email, name, location, avatar, availability, created_at')
            .eq('privacy', 'public');

        if (error) throw error;
        return data || [];
    }
};

// Skills operations using Supabase
export const skillOperations = {
    // Add offered skill
    async addOfferedSkill(skillData) {
        const { data, error } = await supabase
            .from('skills_offered')
            .insert([skillData])
            .select()
            .single();

        if (error) throw error;
        return data;
    },

    // Add wanted skill
    async addWantedSkill(skillData) {
        const { data, error } = await supabase
            .from('skills_wanted')
            .insert([skillData])
            .select()
            .single();

        if (error) throw error;
        return data;
    },

    // Get user's offered skills
    async getUserOfferedSkills(userId) {
        const { data, error } = await supabase
            .from('skills_offered')
            .select('*')
            .eq('user_id', userId);

        if (error) throw error;
        return data || [];
    },

    // Get user's wanted skills
    async getUserWantedSkills(userId) {
        const { data, error } = await supabase
            .from('skills_wanted')
            .select('*')
            .eq('user_id', userId);

        if (error) throw error;
        return data || [];
    },

    // Search offered skills with user info
    async searchOfferedSkills(searchTerm) {
        const { data, error } = await supabase
            .from('skills_offered')
            .select(`
                *,
                users!inner (
                    name,
                    location,
                    avatar,
                    privacy
                )
            `)
            .eq('users.privacy', 'public')
            .or(`skill_name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,category.ilike.%${searchTerm}%`);

        if (error) throw error;
        return data || [];
    },

    // Get all offered skills with user info
    async getAllOfferedSkills() {
        const { data, error } = await supabase
            .from('skills_offered')
            .select(`
                *,
                users!inner (
                    name,
                    location,
                    avatar,
                    privacy
                )
            `)
            .eq('users.privacy', 'public')
            .order('created_at', { ascending: false });

        if (error) throw error;
        return data || [];
    },

    // Get skills by category
    async getSkillsByCategory(category) {
        const { data, error } = await supabase
            .from('skills_offered')
            .select(`
                *,
                users!inner (
                    name,
                    location,
                    avatar,
                    privacy
                )
            `)
            .eq('users.privacy', 'public')
            .eq('category', category);

        if (error) throw error;
        return data || [];
    },

    // Delete offered skill
    async deleteOfferedSkill(skillId, userId) {
        const { error } = await supabase
            .from('skills_offered')
            .delete()
            .eq('id', skillId)
            .eq('user_id', userId);

        if (error) throw error;
        return true;
    },

    // Delete wanted skill
    async deleteWantedSkill(skillId, userId) {
        const { error } = await supabase
            .from('skills_wanted')
            .delete()
            .eq('id', skillId)
            .eq('user_id', userId);

        if (error) throw error;
        return true;
    }
};

// Authentication operations using Supabase
export const authOperations = {
    // Register user with Supabase Auth
    async registerUser(email, password, userData) {
        // Create auth user
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
            email,
            password,
            email_confirm: true,
            user_metadata: userData
        });

        if (authError) throw authError;

        // Create user profile
        const profileData = {
            id: authData.user.id,
            email,
            ...userData,
            created_at: new Date().toISOString()
        };

        const profile = await userOperations.createUser(profileData);
        return { auth: authData, profile };
    },

    // Sign in user
    async signInUser(email, password) {
        const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password
        });

        if (error) throw error;
        return data;
    },

    // Get user session
    async getUser(accessToken) {
        const { data, error } = await supabase.auth.getUser(accessToken);
        if (error) throw error;
        return data.user;
    },

    // Sign out user
    async signOut(accessToken) {
        const { error } = await supabase.auth.admin.signOut(accessToken);
        if (error) throw error;
        return true;
    }
};

// Swap operations using Supabase
export const swapOperations = {
    // Create swap request
    async createSwapRequest(swapData) {
        const { data, error } = await supabase
            .from('swap_requests')
            .insert([swapData])
            .select()
            .single();

        if (error) throw error;
        return data;
    },

    // Get user's swap requests
    async getUserSwapRequests(userId) {
        const { data, error } = await supabase
            .from('swap_requests')
            .select(`
                *,
                requester:users!swap_requests_requester_id_fkey (
                    id, name, avatar, location
                ),
                receiver:users!swap_requests_receiver_id_fkey (
                    id, name, avatar, location
                ),
                offered_skill:skills_offered (
                    id, skill_name, category
                ),
                wanted_skill:skills_wanted (
                    id, skill_name, category
                )
            `)
            .or(`requester_id.eq.${userId},receiver_id.eq.${userId}`)
            .order('created_at', { ascending: false });

        if (error) throw error;
        return data || [];
    },

    // Update swap request status
    async updateSwapStatus(swapId, status, userId) {
        const { data, error } = await supabase
            .from('swap_requests')
            .update({ status, updated_at: new Date().toISOString() })
            .eq('id', swapId)
            .eq('receiver_id', userId) // Only receiver can update status
            .select()
            .single();

        if (error) throw error;
        return data;
    }
};
