/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/mobile-prototype/page";
exports.ids = ["app/mobile-prototype/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmobile-prototype%2Fpage&page=%2Fmobile-prototype%2Fpage&appPaths=%2Fmobile-prototype%2Fpage&pagePath=private-next-app-dir%2Fmobile-prototype%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmobile-prototype%2Fpage&page=%2Fmobile-prototype%2Fpage&appPaths=%2Fmobile-prototype%2Fpage&pagePath=private-next-app-dir%2Fmobile-prototype%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mobile-prototype/page.tsx */ \"(rsc)/./src/app/mobile-prototype/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'mobile-prototype',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/mobile-prototype/page\",\n        pathname: \"/mobile-prototype\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmobile-prototype%2Fpage&page=%2Fmobile-prototype%2Fpage&appPaths=%2Fmobile-prototype%2Fpage&pagePath=private-next-app-dir%2Fmobile-prototype%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(rsc)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/auth-provider.tsx */ \"(rsc)/./src/providers/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/socket-provider.tsx */ \"(rsc)/./src/providers/socket-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNIYWNrYXRob24lNUMlNUNPZG9vJTVDJTVDT2RvbyU1QyU1Q0NsaWVudCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDSGFja2F0aG9uJTVDJTVDT2RvbyU1QyU1Q09kb28lNUMlNUNDbGllbnQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDSGFja2F0aG9uJTVDJTVDT2RvbyU1QyU1Q09kb28lNUMlNUNDbGllbnQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUN0b2FzdGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0hhY2thdGhvbiU1QyU1Q09kb28lNUMlNUNPZG9vJTVDJTVDQ2xpZW50JTVDJTVDc3JjJTVDJTVDcHJvdmlkZXJzJTVDJTVDYXV0aC1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJBdXRoUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0hhY2thdGhvbiU1QyU1Q09kb28lNUMlNUNPZG9vJTVDJTVDQ2xpZW50JTVDJTVDc3JjJTVDJTVDcHJvdmlkZXJzJTVDJTVDc29ja2V0LXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNvY2tldFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBcUk7QUFDckk7QUFDQSw4S0FBMkk7QUFDM0k7QUFDQSxrTEFBK0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxIYWNrYXRob25cXFxcT2Rvb1xcXFxPZG9vXFxcXENsaWVudFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFx1aVxcXFx0b2FzdGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiQzpcXFxcSGFja2F0aG9uXFxcXE9kb29cXFxcT2Rvb1xcXFxDbGllbnRcXFxcc3JjXFxcXHByb3ZpZGVyc1xcXFxhdXRoLXByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU29ja2V0UHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxIYWNrYXRob25cXFxcT2Rvb1xcXFxPZG9vXFxcXENsaWVudFxcXFxzcmNcXFxccHJvdmlkZXJzXFxcXHNvY2tldC1wcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cmobile-prototype%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cmobile-prototype%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mobile-prototype/page.tsx */ \"(rsc)/./src/app/mobile-prototype/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNIYWNrYXRob24lNUMlNUNPZG9vJTVDJTVDT2RvbyU1QyU1Q0NsaWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q21vYmlsZS1wcm90b3R5cGUlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQTRHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxIYWNrYXRob25cXFxcT2Rvb1xcXFxPZG9vXFxcXENsaWVudFxcXFxzcmNcXFxcYXBwXFxcXG1vYmlsZS1wcm90b3R5cGVcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cmobile-prototype%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"17d53ceeddfa\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcSGFja2F0aG9uXFxPZG9vXFxPZG9vXFxDbGllbnRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE3ZDUzY2VlZGRmYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../providers/auth-provider */ \"(rsc)/./src/providers/auth-provider.tsx\");\n/* harmony import */ var _providers_socket_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../providers/socket-provider */ \"(rsc)/./src/providers/socket-provider.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ui/toaster */ \"(rsc)/./src/components/ui/toaster.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: 'SkillSwap - Exchange Skills, Build Connections',\n    description: 'A frictionless platform for exchanging skills and building meaningful connections through knowledge sharing.',\n    keywords: [\n        'skill exchange',\n        'learning',\n        'networking',\n        'professional development'\n    ],\n    authors: [\n        {\n            name: 'SkillSwap Team'\n        }\n    ],\n    creator: 'SkillSwap Platform',\n    publisher: 'SkillSwap',\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL('https://skillswap.com'),\n    alternates: {\n        canonical: '/'\n    },\n    openGraph: {\n        title: 'SkillSwap - Exchange Skills, Build Connections',\n        description: 'A frictionless platform for exchanging skills and building meaningful connections through knowledge sharing.',\n        url: 'https://skillswap.com',\n        siteName: 'SkillSwap',\n        locale: 'en_US',\n        type: 'website'\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'SkillSwap - Exchange Skills, Build Connections',\n        description: 'A frictionless platform for exchanging skills and building meaningful connections through knowledge sharing.',\n        creator: '@skillswap'\n    },\n    verification: {\n        google: 'google-site-verification-token'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} h-full antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_socket_provider__WEBPACK_IMPORTED_MODULE_3__.SocketProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"min-h-full\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_4__.Toaster, {}, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/mobile-prototype/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/mobile-prototype/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\app\\mobile-prototype\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster),
/* harmony export */   toast: () => (/* binding */ toast)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\ui\\toaster.tsx",
"Toaster",
);const toast = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\components\\ui\\toaster.tsx",
"toast",
);

/***/ }),

/***/ "(rsc)/./src/providers/auth-provider.tsx":
/*!*****************************************!*\
  !*** ./src/providers/auth-provider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuthContext: () => (/* binding */ useAuthContext)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\auth-provider.tsx",
"AuthProvider",
);const useAuthContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuthContext() from the server but useAuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\auth-provider.tsx",
"useAuthContext",
);

/***/ }),

/***/ "(rsc)/./src/providers/socket-provider.tsx":
/*!*******************************************!*\
  !*** ./src/providers/socket-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),
/* harmony export */   useSocket: () => (/* binding */ useSocket)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SocketProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SocketProvider() from the server but SocketProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\socket-provider.tsx",
"SocketProvider",
);const useSocket = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSocket() from the server but useSocket is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Hackathon\\Odoo\\Odoo\\Client\\src\\providers\\socket-provider.tsx",
"useSocket",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(ssr)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/auth-provider.tsx */ \"(ssr)/./src/providers/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/socket-provider.tsx */ \"(ssr)/./src/providers/socket-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Cproviders%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cmobile-prototype%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cmobile-prototype%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mobile-prototype/page.tsx */ \"(ssr)/./src/app/mobile-prototype/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNIYWNrYXRob24lNUMlNUNPZG9vJTVDJTVDT2RvbyU1QyU1Q0NsaWVudCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q21vYmlsZS1wcm90b3R5cGUlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQTRHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxIYWNrYXRob25cXFxcT2Rvb1xcXFxPZG9vXFxcXENsaWVudFxcXFxzcmNcXFxcYXBwXFxcXG1vYmlsZS1wcm90b3R5cGVcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Capp%5C%5Cmobile-prototype%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/mobile-prototype/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/mobile-prototype/page.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobilePrototype)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_mobile_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/mobile-layout */ \"(ssr)/./src/components/ui/mobile-layout.tsx\");\n/* harmony import */ var _components_ui_pinterest_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/pinterest-card */ \"(ssr)/./src/components/ui/pinterest-card.tsx\");\n/* harmony import */ var _components_ui_mobile_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/mobile-components */ \"(ssr)/./src/components/ui/mobile-components.tsx\");\n/* harmony import */ var _components_ui_skillswap_logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skillswap-logo */ \"(ssr)/./src/components/ui/skillswap-logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst sampleSkills = [\n    {\n        id: '1',\n        name: 'React Development',\n        level: 'Expert',\n        description: 'Building modern web applications with React, hooks, and TypeScript',\n        examples: [\n            'Custom Hooks',\n            'State Management',\n            'Performance Optimization'\n        ]\n    },\n    {\n        id: '2',\n        name: 'UI/UX Design',\n        level: 'Intermediate',\n        description: 'Creating beautiful and intuitive user interfaces with modern design principles',\n        examples: [\n            'Figma',\n            'Prototyping',\n            'User Research'\n        ]\n    },\n    {\n        id: '3',\n        name: 'Photography',\n        level: 'Beginner',\n        description: 'Capturing moments and learning composition, lighting, and post-processing',\n        examples: [\n            'Portrait',\n            'Landscape',\n            'Street Photography'\n        ]\n    }\n];\nconst sampleContent = [\n    {\n        id: '1',\n        title: 'Learn About Web Development',\n        subtitle: 'Technology',\n        content: 'Master modern web development techniques and build amazing applications that users love.',\n        category: 'Development',\n        progress: 23\n    },\n    {\n        id: '2',\n        title: 'Design Fundamentals',\n        subtitle: 'Creative',\n        content: 'Understand the principles of good design and create beautiful, functional interfaces.',\n        category: 'Design',\n        progress: 47\n    },\n    {\n        id: '3',\n        title: 'Digital Marketing',\n        subtitle: 'Business',\n        content: 'Learn how to effectively market products and services in the digital age.',\n        category: 'Marketing',\n        progress: 12\n    }\n];\nfunction MobilePrototype() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('explore');\n    const [selectedSkills, setSelectedSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchValue, setSearchValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const tabs = [\n        {\n            id: 'explore',\n            label: 'Explore',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this),\n            count: 124\n        },\n        {\n            id: 'my-skills',\n            label: 'My Skills',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this),\n            count: 8\n        },\n        {\n            id: 'matches',\n            label: 'Matches',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this),\n            count: 3\n        }\n    ];\n    const toggleSkillSelection = (skillId)=>{\n        setSelectedSkills((prev)=>prev.includes(skillId) ? prev.filter((id)=>id !== skillId) : [\n                ...prev,\n                skillId\n            ]);\n    };\n    const renderExploreTab = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.Section, {\n                    title: \"Your Learning Journey\",\n                    subtitle: \"Complete your profile to unlock more matches\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-card p-6 shadow-soft\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_components__WEBPACK_IMPORTED_MODULE_4__.ProgressSteps, {\n                            currentStep: 2,\n                            totalSteps: 3,\n                            labels: [\n                                'Profile',\n                                'Skills',\n                                'Preferences'\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.Section, {\n                    title: \"Recommended for You\",\n                    subtitle: \"Based on your interests\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.ContentGrid, {\n                        columns: 1,\n                        gap: \"md\",\n                        children: sampleContent.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pinterest_card__WEBPACK_IMPORTED_MODULE_3__.PinterestCard, {\n                                title: item.title,\n                                subtitle: item.subtitle,\n                                content: item.content,\n                                category: item.category,\n                                progress: item.progress,\n                                onAction: ()=>console.log('Clicked:', item.title),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-ghost text-sm\",\n                                            children: \"Learn More\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-primary text-sm\",\n                                            children: \"Start Learning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            }, item.id, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n            lineNumber: 107,\n            columnNumber: 5\n        }, this);\n    const renderMySkillsTab = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.Section, {\n            title: \"Your Skills\",\n            subtitle: \"Manage your skills and expertise levels\",\n            action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"text-body-medium text-primary-600 font-medium\",\n                children: \"Add Skill\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.ContentGrid, {\n                columns: 1,\n                gap: \"md\",\n                children: sampleSkills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pinterest_card__WEBPACK_IMPORTED_MODULE_3__.SkillCard, {\n                        skillName: skill.name,\n                        level: skill.level,\n                        description: skill.description,\n                        examples: skill.examples,\n                        isSelected: selectedSkills.includes(skill.id),\n                        onToggle: ()=>toggleSkillSelection(skill.id)\n                    }, skill.id, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n            lineNumber: 151,\n            columnNumber: 5\n        }, this);\n    const renderMatchesTab = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.Section, {\n            title: \"Skill Matches\",\n            subtitle: \"People looking to learn what you can teach\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: Array.from({\n                    length: 3\n                }, (_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-card p-6 shadow-soft\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                    children: String.fromCharCode(65 + i)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-title font-semibold text-gray-900\",\n                                            children: \"Sarah Johnson\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-body-medium text-gray-600 mb-2\",\n                                            children: \"Wants to learn React Development\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"skill-tag\",\n                                                    children: \"JavaScript\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"skill-tag\",\n                                                    children: \"HTML/CSS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn-secondary text-sm\",\n                                                    children: \"View Profile\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"btn-primary text-sm\",\n                                                    children: \"Connect\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, i, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n            lineNumber: 177,\n            columnNumber: 5\n        }, this);\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'explore':\n                return renderExploreTab();\n            case 'my-skills':\n                return renderMySkillsTab();\n            case 'matches':\n                return renderMatchesTab();\n            default:\n                return renderExploreTab();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.MobileLayout, {\n        showSearch: false,\n        rightAction: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            className: \"p-2 rounded-button hover:bg-gray-100 transition-colors duration-200\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-6 h-6 text-gray-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 17h5l-5 5v-5z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M4 17v-7a8 8 0 018-8 8 8 0 018 8v7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 11\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n            lineNumber: 232,\n            columnNumber: 9\n        }, void 0),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 pt-4 pl-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mr-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skillswap_logo__WEBPACK_IMPORTED_MODULE_5__.SkillSwapLogo, {\n                                    size: \"lg\",\n                                    href: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center flex-1 pr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                        children: \"Mobile Prototype Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Interactive skill-sharing interface showcase\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'explore' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_components__WEBPACK_IMPORTED_MODULE_4__.SearchBar, {\n                            value: searchValue,\n                            onChange: setSearchValue,\n                            placeholder: \"Search skills, people, or topics...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_layout__WEBPACK_IMPORTED_MODULE_2__.Tabs, {\n                tabs: tabs,\n                activeTab: activeTab,\n                onChange: setActiveTab\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pb-20\",\n                children: renderTabContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_mobile_components__WEBPACK_IMPORTED_MODULE_4__.FloatingActionButton, {\n                onClick: ()=>console.log('Start new swap'),\n                label: \"Start Swap\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-6 h-6\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 4v16m8-8H4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\mobile-prototype\\\\page.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/mobile-prototype/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/mobile-components.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/mobile-components.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingActionButton: () => (/* binding */ FloatingActionButton),\n/* harmony export */   NavigationHeader: () => (/* binding */ NavigationHeader),\n/* harmony export */   ProgressSteps: () => (/* binding */ ProgressSteps),\n/* harmony export */   SearchBar: () => (/* binding */ SearchBar),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ProgressSteps,NavigationHeader,FloatingActionButton,SearchBar,default auto */ \n\n\n\nfunction ProgressSteps({ currentStep, totalSteps, labels, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center justify-center space-x-4', className),\n        children: Array.from({\n            length: totalSteps\n        }, (_, index)=>{\n            const stepNumber = index + 1;\n            const isActive = stepNumber === currentStep;\n            const isCompleted = stepNumber < currentStep;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('progress-step', isActive && 'progress-step-active', isCompleted && 'progress-step-completed')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 15\n                            }, this),\n                            labels && labels[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('mt-2 text-caption font-medium', isActive ? 'text-primary-600' : 'text-gray-500'),\n                                children: labels[index]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 13\n                    }, this),\n                    index < totalSteps - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-0.5 mx-2 bg-gray-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                lineNumber: 23,\n                columnNumber: 11\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\nfunction NavigationHeader({ title, subtitle, onBack, rightAction, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center justify-between py-4 px-6 bg-white border-b border-gray-100', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"p-2 rounded-button hover:bg-gray-100 transition-colors duration-200\",\n                        \"aria-label\": \"Go back\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5 text-gray-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M15 19l-7-7 7-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2 hover:opacity-80 transition-opacity duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6 text-blue-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM15 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2h-2zM5 13a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM15 13a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2h-2z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-1 -right-1 w-2 h-2 bg-purple-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-bold text-gray-900\",\n                                children: \"SkillSwap\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-headline font-semibold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-body-medium text-gray-600\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            rightAction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: rightAction\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\nfunction FloatingActionButton({ onClick, icon, label, position = 'bottom-right', className }) {\n    const positionClasses = {\n        'bottom-right': 'bottom-6 right-6',\n        'bottom-left': 'bottom-6 left-6',\n        'top-right': 'top-6 right-6',\n        'top-left': 'top-6 left-6'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('fixed z-50 w-14 h-14 bg-primary-500 hover:bg-primary-600 text-white rounded-full shadow-strong hover:shadow-xl transition-all duration-300 flex items-center justify-center group', positionClasses[position], className),\n        \"aria-label\": label,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"transform group-hover:scale-110 transition-transform duration-200\",\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute right-full mr-3 px-3 py-2 bg-gray-900 text-white text-caption rounded-button opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\nfunction SearchBar({ value, onChange, placeholder = \"Search...\", onFocus, onBlur, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"h-5 w-5 text-gray-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: placeholder,\n                onFocus: onFocus,\n                onBlur: onBlur,\n                className: \"input-base pl-11 pr-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-components.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProgressSteps);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/mobile-components.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/mobile-layout.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/mobile-layout.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentGrid: () => (/* binding */ ContentGrid),\n/* harmony export */   MobileLayout: () => (/* binding */ MobileLayout),\n/* harmony export */   Section: () => (/* binding */ Section),\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _mobile_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mobile-components */ \"(ssr)/./src/components/ui/mobile-components.tsx\");\n/* __next_internal_client_entry_do_not_use__ MobileLayout,ContentGrid,Section,Tabs,default auto */ \n\n\n\nfunction MobileLayout({ children, title, subtitle, showBack = false, showSearch = false, searchValue = '', onSearchChange, rightAction, className, backgroundPattern = 'warm' }) {\n    const backgroundClasses = {\n        warm: 'bg-warm-200',\n        gradient: 'bg-gradient-warm',\n        plain: 'bg-white'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('min-h-screen flex flex-col', backgroundClasses[backgroundPattern], className),\n        children: [\n            (title || showSearch) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sticky top-0 z-40 bg-white/80 backdrop-blur-md border-b border-gray-100\",\n                children: [\n                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_components__WEBPACK_IMPORTED_MODULE_3__.NavigationHeader, {\n                        title: title,\n                        subtitle: subtitle,\n                        onBack: showBack ? ()=>window.history.back() : undefined,\n                        rightAction: rightAction\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, this),\n                    showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 pb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_components__WEBPACK_IMPORTED_MODULE_3__.SearchBar, {\n                            value: searchValue,\n                            onChange: onSearchChange || (()=>{}),\n                            placeholder: \"Search skills, people, or topics...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 relative\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 overflow-hidden pointer-events-none z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary-200/20 to-primary-400/20 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-warm-300/30 to-warm-500/30 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\nfunction ContentGrid({ children, columns = 1, gap = 'md', className }) {\n    const gridClasses = {\n        1: 'grid-cols-1',\n        2: 'grid-cols-1 sm:grid-cols-2',\n        3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'\n    };\n    const gapClasses = {\n        sm: 'gap-3',\n        md: 'gap-4',\n        lg: 'gap-6'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('grid', gridClasses[columns], gapClasses[gap], 'p-4 relative z-10', className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\nfunction Section({ title, subtitle, action, children, className, spacing = 'normal' }) {\n    const spacingClasses = {\n        tight: 'space-y-3',\n        normal: 'space-y-4',\n        loose: 'space-y-6'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('relative z-10', spacingClasses[spacing], className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-display-small font-semibold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-body-medium text-gray-600 mt-1\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: action\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction Tabs({ tabs, activeTab, onChange, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-white border-b border-gray-100', className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex overflow-x-auto scrollbar-hide\",\n            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onChange(tab.id),\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center space-x-2 px-6 py-4 text-body-medium font-medium whitespace-nowrap border-b-2 transition-all duration-200', activeTab === tab.id ? 'border-primary-500 text-primary-600 bg-primary-50/50' : 'border-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-50'),\n                    children: [\n                        tab.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex-shrink-0', activeTab === tab.id ? 'text-primary-600' : 'text-gray-400'),\n                            children: tab.icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: tab.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this),\n                        tab.count !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded-full', activeTab === tab.id ? 'bg-primary-100 text-primary-800' : 'bg-gray-100 text-gray-600'),\n                            children: tab.count\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, tab.id, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\mobile-layout.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobileLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/mobile-layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/pinterest-card.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/pinterest-card.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PinterestCard: () => (/* binding */ PinterestCard),\n/* harmony export */   SkillCard: () => (/* binding */ SkillCard),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ PinterestCard,SkillCard,default auto */ \n\n\nfunction PinterestCard({ title, subtitle, content, imageUrl, progress, category, isActive = false, onAction, className, children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('card-mobile group cursor-pointer transform transition-all duration-300 hover:scale-[1.02]', isActive && 'ring-2 ring-primary-500 shadow-strong', className),\n        onClick: onAction,\n        children: [\n            category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center px-3 py-1 rounded-chip text-caption font-medium bg-white/90 text-gray-700 backdrop-blur-sm border border-gray-200\",\n                    children: category\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this),\n            progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 left-4 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"progress-circle\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-semibold\",\n                        children: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-caption text-gray-500 mb-1 uppercase tracking-wide font-medium\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-headline font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-200\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-body-medium text-gray-600 leading-relaxed\",\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-auto\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-1 -right-1 w-16 h-16 bg-gradient-to-br from-primary-400/20 to-primary-600/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -bottom-1 -left-1 w-12 h-12 bg-gradient-to-br from-warm-300/30 to-warm-400/30 rounded-full blur-lg opacity-50\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\nfunction SkillCard({ skillName, level, description, examples, isSelected = false, onToggle }) {\n    const levelColors = {\n        Beginner: 'bg-green-100 text-green-800 border-green-200',\n        Intermediate: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n        Expert: 'bg-red-100 text-red-800 border-red-200'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('content-card cursor-pointer', isSelected && 'ring-2 ring-primary-500 bg-gradient-content'),\n        onClick: onToggle,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between mb-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-title font-semibold text-gray-900\",\n                        children: skillName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('inline-flex items-center px-2 py-1 rounded-chip text-xs font-medium border', levelColors[level]),\n                        children: level\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-body-medium text-gray-600 mb-4\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-caption font-medium text-gray-700 uppercase tracking-wide\",\n                        children: \"Examples:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: examples.map((example, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-3 py-1 rounded-chip text-caption bg-gray-100 text-gray-600 border border-gray-200\",\n                                children: example\n                            }, index, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\pinterest-card.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PinterestCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/pinterest-card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/skillswap-logo.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/skillswap-logo.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomeLink: () => (/* binding */ HomeLink),\n/* harmony export */   SkillSwapLogo: () => (/* binding */ SkillSwapLogo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ SkillSwapLogo,HomeLink auto */ \n\n\n\nfunction SkillSwapLogo({ size = 'md', showText = true, className, textClassName, href = '/' }) {\n    const sizeClasses = {\n        sm: 'w-5 h-5',\n        md: 'w-6 h-6',\n        lg: 'w-8 h-8'\n    };\n    const textSizeClasses = {\n        sm: 'text-base',\n        md: 'text-lg',\n        lg: 'text-2xl'\n    };\n    const dotSizeClasses = {\n        sm: 'w-1.5 h-1.5',\n        md: 'w-2 h-2',\n        lg: 'w-3 h-3'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: href,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('inline-flex items-center gap-2 hover:opacity-80 transition-opacity duration-200', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-blue-600', sizeClasses[size]),\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM15 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2h-2zM5 13a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM15 13a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2h-2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\skillswap-logo.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\skillswap-logo.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('absolute -top-0.5 -right-0.5 bg-purple-500 rounded-full animate-pulse', dotSizeClasses[size])\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\skillswap-logo.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\skillswap-logo.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('font-bold text-gray-900', textSizeClasses[size], textClassName),\n                children: \"SkillSwap\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\skillswap-logo.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\skillswap-logo.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n// Quick homepage link component\nfunction HomeLink({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n        href: \"/\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200', className),\n        children: children || '← Back to SkillSwap'\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\skillswap-logo.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/skillswap-logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/InformationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,InformationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast auto */ \n\n\n\n// Simple toast store (in a real app, you might use Zustand or similar)\nconst toastStore = {\n    toasts: [],\n    addToast: ()=>{},\n    removeToast: ()=>{}\n};\nconst icons = {\n    success: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    error: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    warning: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    info: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n};\nconst colors = {\n    success: {\n        bg: 'bg-green-50',\n        border: 'border-green-200',\n        icon: 'text-green-600',\n        title: 'text-green-900',\n        message: 'text-green-700'\n    },\n    error: {\n        bg: 'bg-red-50',\n        border: 'border-red-200',\n        icon: 'text-red-600',\n        title: 'text-red-900',\n        message: 'text-red-700'\n    },\n    warning: {\n        bg: 'bg-yellow-50',\n        border: 'border-yellow-200',\n        icon: 'text-yellow-600',\n        title: 'text-yellow-900',\n        message: 'text-yellow-700'\n    },\n    info: {\n        bg: 'bg-blue-50',\n        border: 'border-blue-200',\n        icon: 'text-blue-600',\n        title: 'text-blue-900',\n        message: 'text-blue-700'\n    }\n};\nfunction Toaster() {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toaster.useEffect\": ()=>{\n            toastStore.addToast = ({\n                \"Toaster.useEffect\": (toast)=>{\n                    const id = Math.random().toString(36).substr(2, 9);\n                    const newToast = {\n                        ...toast,\n                        id\n                    };\n                    setToasts({\n                        \"Toaster.useEffect\": (prev)=>[\n                                ...prev,\n                                newToast\n                            ]\n                    }[\"Toaster.useEffect\"]);\n                    // Auto remove after duration\n                    setTimeout({\n                        \"Toaster.useEffect\": ()=>{\n                            toastStore.removeToast(id);\n                        }\n                    }[\"Toaster.useEffect\"], toast.duration || 5000);\n                }\n            })[\"Toaster.useEffect\"];\n            toastStore.removeToast = ({\n                \"Toaster.useEffect\": (id)=>{\n                    setToasts({\n                        \"Toaster.useEffect\": (prev)=>prev.filter({\n                                \"Toaster.useEffect\": (toast)=>toast.id !== id\n                            }[\"Toaster.useEffect\"])\n                    }[\"Toaster.useEffect\"]);\n                }\n            })[\"Toaster.useEffect\"];\n        }\n    }[\"Toaster.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 w-full max-w-sm space-y-2\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n            children: toasts.map((toast)=>{\n                const Icon = icons[toast.type];\n                const color = colors[toast.type];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        x: 100,\n                        scale: 0.95\n                    },\n                    animate: {\n                        opacity: 1,\n                        x: 0,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        x: 100,\n                        scale: 0.95\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: `relative rounded-lg border p-4 shadow-lg ${color.bg} ${color.border}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                className: `h-5 w-5 mt-0.5 mr-3 flex-shrink-0 ${color.icon}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `text-sm font-medium ${color.title}`,\n                                        children: toast.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 19\n                                    }, this),\n                                    toast.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `mt-1 text-sm ${color.message}`,\n                                        children: toast.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>toastStore.removeToast(toast.id),\n                                className: `ml-3 inline-flex rounded-md p-1.5 hover:bg-black/5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${color.icon}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Dismiss\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_InformationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 15\n                    }, this)\n                }, toast.id, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n// Export toast function for use throughout the app\nconst toast = {\n    success: (title, message, duration)=>toastStore.addToast({\n            type: 'success',\n            title,\n            message,\n            duration\n        }),\n    error: (title, message, duration)=>toastStore.addToast({\n            type: 'error',\n            title,\n            message,\n            duration\n        }),\n    warning: (title, message, duration)=>toastStore.addToast({\n            type: 'warning',\n            title,\n            message,\n            duration\n        }),\n    info: (title, message, duration)=>toastStore.addToast({\n            type: 'info',\n            title,\n            message,\n            duration\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-auth.ts":
/*!*******************************!*\
  !*** ./src/hooks/use-auth.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var _store_auth_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../store/auth-store */ \"(ssr)/./src/store/auth-store.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \nfunction useAuth() {\n    const { user, isAuthenticated, isLoading, login, logout, updateProfile } = (0,_store_auth_store__WEBPACK_IMPORTED_MODULE_0__.useAuthStore)();\n    return {\n        user,\n        isAuthenticated,\n        isLoading,\n        login,\n        logout,\n        updateProfile\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlLWF1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7NkRBRWtEO0FBRTNDLFNBQVNDO0lBQ2QsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLGVBQWUsRUFBRUMsU0FBUyxFQUFFQyxLQUFLLEVBQUVDLE1BQU0sRUFBRUMsYUFBYSxFQUFFLEdBQUdQLCtEQUFZQTtJQUV2RixPQUFPO1FBQ0xFO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO1FBQ0FDO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXEhhY2thdGhvblxcT2Rvb1xcT2Rvb1xcQ2xpZW50XFxzcmNcXGhvb2tzXFx1c2UtYXV0aC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCB7IHVzZUF1dGhTdG9yZSB9IGZyb20gJy4uL3N0b3JlL2F1dGgtc3RvcmUnXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gdXNlQXV0aCgpIHtcclxuICBjb25zdCB7IHVzZXIsIGlzQXV0aGVudGljYXRlZCwgaXNMb2FkaW5nLCBsb2dpbiwgbG9nb3V0LCB1cGRhdGVQcm9maWxlIH0gPSB1c2VBdXRoU3RvcmUoKVxyXG5cclxuICByZXR1cm4ge1xyXG4gICAgdXNlcixcclxuICAgIGlzQXV0aGVudGljYXRlZCxcclxuICAgIGlzTG9hZGluZyxcclxuICAgIGxvZ2luLFxyXG4gICAgbG9nb3V0LFxyXG4gICAgdXBkYXRlUHJvZmlsZSxcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZUF1dGhTdG9yZSIsInVzZUF1dGgiLCJ1c2VyIiwiaXNBdXRoZW50aWNhdGVkIiwiaXNMb2FkaW5nIiwibG9naW4iLCJsb2dvdXQiLCJ1cGRhdGVQcm9maWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirst: () => (/* binding */ capitalizeFirst),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(new Date(date));\n}\nfunction formatRelativeTime(date) {\n    const now = new Date();\n    const targetDate = new Date(date);\n    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return formatDate(date);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength - 3) + '...';\n}\nfunction capitalizeFirst(str) {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n}\nfunction slugify(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, '').replace(/[\\s_-]+/g, '-').replace(/^-+|-+$/g, '');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/auth-provider.tsx":
/*!*****************************************!*\
  !*** ./src/providers/auth-provider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuthContext: () => (/* binding */ useAuthContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_auth_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../store/auth-store */ \"(ssr)/./src/store/auth-store.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuthContext auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nfunction AuthProvider({ children }) {\n    const setUser = (0,_store_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)({\n        \"AuthProvider.useAuthStore[setUser]\": (state)=>state.setUser\n    }[\"AuthProvider.useAuthStore[setUser]\"]);\n    const setLoading = (0,_store_auth_store__WEBPACK_IMPORTED_MODULE_2__.useAuthStore)({\n        \"AuthProvider.useAuthStore[setLoading]\": (state)=>state.setLoading\n    }[\"AuthProvider.useAuthStore[setLoading]\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check if user is authenticated on app load\n            const checkAuth = {\n                \"AuthProvider.useEffect.checkAuth\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const response = await fetch('/api/auth/me', {\n                            credentials: 'include'\n                        });\n                        if (response.ok) {\n                            const { user } = await response.json();\n                            setUser(user);\n                        } else {\n                            setUser(null);\n                        }\n                    } catch (error) {\n                        console.error('Auth check failed:', error);\n                        setUser(null);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkAuth\"];\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        setUser,\n        setLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {},\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\providers\\\\auth-provider.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuthContext() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuthContext must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/socket-provider.tsx":
/*!*******************************************!*\
  !*** ./src/providers/socket-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* harmony import */ var _hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/use-auth */ \"(ssr)/./src/hooks/use-auth.ts\");\n/* __next_internal_client_entry_do_not_use__ SocketProvider,useSocket auto */ \n\n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    socket: null,\n    isConnected: false\n});\nfunction SocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, isAuthenticated } = (0,_hooks_use_auth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SocketProvider.useEffect\": ()=>{\n            if (isAuthenticated && user) {\n                // Initialize socket connection\n                const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001', {\n                    auth: {\n                        userId: user.id\n                    },\n                    autoConnect: true\n                });\n                socketInstance.on('connect', {\n                    \"SocketProvider.useEffect\": ()=>{\n                        console.log('Socket connected:', socketInstance.id);\n                        setIsConnected(true);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                socketInstance.on('disconnect', {\n                    \"SocketProvider.useEffect\": ()=>{\n                        console.log('Socket disconnected');\n                        setIsConnected(false);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                socketInstance.on('connect_error', {\n                    \"SocketProvider.useEffect\": (error)=>{\n                        console.error('Socket connection error:', error);\n                        setIsConnected(false);\n                    }\n                }[\"SocketProvider.useEffect\"]);\n                setSocket(socketInstance);\n                return ({\n                    \"SocketProvider.useEffect\": ()=>{\n                        socketInstance.disconnect();\n                        setSocket(null);\n                        setIsConnected(false);\n                    }\n                })[\"SocketProvider.useEffect\"];\n            } else {\n                // Disconnect socket if user is not authenticated\n                if (socket) {\n                    socket.disconnect();\n                    setSocket(null);\n                    setIsConnected(false);\n                }\n            }\n        }\n    }[\"SocketProvider.useEffect\"], [\n        isAuthenticated,\n        user?.id\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: {\n            socket,\n            isConnected\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\providers\\\\socket-provider.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\nfunction useSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n    if (context === undefined) {\n        throw new Error('useSocket must be used within a SocketProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/socket-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/store/auth-store.ts":
/*!*********************************!*\
  !*** ./src/store/auth-store.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        setUser: (user)=>set({\n                user,\n                isAuthenticated: !!user\n            }),\n        setLoading: (isLoading)=>set({\n                isLoading\n            }),\n        login: async (email, password)=>{\n            try {\n                set({\n                    isLoading: true\n                });\n                const response = await fetch('/api/auth/login', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        email,\n                        password\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error('Login failed');\n                }\n                const { user } = await response.json();\n                set({\n                    user,\n                    isAuthenticated: true,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        },\n        logout: ()=>{\n            // Clear auth state\n            set({\n                user: null,\n                isAuthenticated: false\n            });\n            // Call logout API\n            fetch('/api/auth/logout', {\n                method: 'POST'\n            }).catch(console.error);\n        },\n        updateProfile: async (data)=>{\n            try {\n                const currentUser = get().user;\n                if (!currentUser) throw new Error('No user logged in');\n                set({\n                    isLoading: true\n                });\n                const response = await fetch('/api/user/profile', {\n                    method: 'PATCH',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (!response.ok) {\n                    throw new Error('Profile update failed');\n                }\n                const { user } = await response.json();\n                set({\n                    user,\n                    isLoading: false\n                });\n            } catch (error) {\n                set({\n                    isLoading: false\n                });\n                throw error;\n            }\n        }\n    }), {\n    name: 'auth-storage',\n    partialize: (state)=>({\n            user: state.user,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/auth-store.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/ws","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@heroicons","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/zustand","vendor-chunks/motion-utils","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmobile-prototype%2Fpage&page=%2Fmobile-prototype%2Fpage&appPaths=%2Fmobile-prototype%2Fpage&pagePath=private-next-app-dir%2Fmobile-prototype%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();