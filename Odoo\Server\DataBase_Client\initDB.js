import { supabase } from './skillSwapDB.js';
import fs from 'fs';
import path from 'path';

// Function to initialize database with schema
export const initializeDatabase = async () => {
    try {
        console.log('Initializing database...');
        
        // Read and execute schema.sql
        const schemaPath = path.join(process.cwd(), 'DataBase_Client', 'schema.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');
        
        // Split by semicolon and execute each statement
        const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);
        
        for (const statement of statements) {
            if (statement.trim()) {
                await executeQuery(statement);
            }
        }
        
        console.log('Database initialized successfully!');
        return true;
        
    } catch (error) {
        console.error('Error initializing database:', error);
        throw error;
    }
};

// Function to check if tables exist
export const checkTables = async () => {
    try {
        const query = `
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
        `;
        
        const result = await executeQuery(query);
        const tableNames = result.map(row => row.table_name);
        
        console.log('Existing tables:', tableNames);
        
        const requiredTables = ['users', 'skills_offered', 'skills_wanted'];
        const missingTables = requiredTables.filter(table => !tableNames.includes(table));
        
        if (missingTables.length > 0) {
            console.log('Missing tables:', missingTables);
            return false;
        }
        
        console.log('All required tables exist!');
        return true;
        
    } catch (error) {
        console.error('Error checking tables:', error);
        return false;
    }
};

// Run initialization if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    try {
        const tablesExist = await checkTables();
        
        if (!tablesExist) {
            await initializeDatabase();
        }
        
        process.exit(0);
    } catch (error) {
        console.error('Database initialization failed:', error);
        process.exit(1);
    }
}
