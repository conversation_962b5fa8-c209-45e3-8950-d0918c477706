"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/section.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/section.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Section: () => (/* binding */ Section),\n/* harmony export */   SectionGrid: () => (/* binding */ SectionGrid),\n/* harmony export */   SectionHeader: () => (/* binding */ SectionHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _animated_section__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./animated-section */ \"(app-pages-browser)/./src/components/ui/animated-section.tsx\");\n/* __next_internal_client_entry_do_not_use__ Section,SectionHeader,SectionGrid auto */ \n\n\n\nconst backgroundVariants = {\n    white: 'bg-white',\n    gray: 'bg-gray-50',\n    gradient: 'bg-gradient-to-br from-blue-50 via-white to-purple-50',\n    warm: 'bg-warm-200'\n};\nconst paddingVariants = {\n    sm: 'py-12',\n    md: 'py-16',\n    lg: 'py-20',\n    xl: 'py-24'\n};\nconst maxWidthVariants = {\n    sm: 'max-w-sm',\n    md: 'max-w-md',\n    lg: 'max-w-lg',\n    xl: 'max-w-xl',\n    '2xl': 'max-w-2xl',\n    '4xl': 'max-w-4xl',\n    '6xl': 'max-w-6xl',\n    '7xl': 'max-w-7xl',\n    full: 'max-w-full'\n};\nfunction Section(param) {\n    let { children, className, containerClassName, background = 'white', padding = 'lg', animated = true, maxWidth = '7xl', id } = param;\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(backgroundVariants[background], paddingVariants[padding], className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('mx-auto px-6 lg:px-8', maxWidthVariants[maxWidth], containerClassName),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n    if (animated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedSection, {\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this);\n    }\n    return content;\n}\n_c = Section;\nfunction SectionHeader(param) {\n    let { title, subtitle, description, centered = true, className, animated = true } = param;\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(centered ? 'text-center' : 'text-left', 'mb-16', className),\n        children: [\n            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm font-semibold text-primary-600 uppercase tracking-wide mb-3\",\n                children: subtitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n    if (animated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedSection, {\n            delay: 0.1,\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    return content;\n}\n_c1 = SectionHeader;\nfunction SectionGrid(param) {\n    let { children, columns = 3, gap = 'lg', className } = param;\n    const columnVariants = {\n        1: 'grid-cols-1',\n        2: 'grid-cols-1 md:grid-cols-2',\n        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n        4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'\n    };\n    const gapVariants = {\n        sm: 'gap-6',\n        md: 'gap-8',\n        lg: 'gap-12'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('grid', columnVariants[columns], gapVariants[gap], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_c2 = SectionGrid;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Section\");\n$RefreshReg$(_c1, \"SectionHeader\");\n$RefreshReg$(_c2, \"SectionGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/section.tsx\n"));

/***/ })

});