"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/features-section.tsx":
/*!*****************************************************!*\
  !*** ./src/components/landing/features-section.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeaturesSection: () => (/* binding */ FeaturesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,GlobeAltIcon,LightBulbIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,GlobeAltIcon,LightBulbIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,GlobeAltIcon,LightBulbIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,GlobeAltIcon,LightBulbIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,GlobeAltIcon,LightBulbIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,GlobeAltIcon,LightBulbIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,GlobeAltIcon,LightBulbIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ClockIcon,GlobeAltIcon,LightBulbIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ FeaturesSection auto */ \n\nconst features = [\n    {\n        name: 'AI-Powered Matching',\n        description: 'Our intelligent algorithm finds the perfect skill partners based on compatibility, learning style, and availability.',\n        icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        gradient: 'from-blue-500 to-purple-600'\n    },\n    {\n        name: 'Secure & Private',\n        description: 'End-to-end encryption, verified profiles, and granular privacy controls keep your information safe.',\n        icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        gradient: 'from-green-500 to-teal-600'\n    },\n    {\n        name: 'Instant Messaging',\n        description: 'Real-time chat with file sharing, video calls, and seamless swap request negotiation built-in.',\n        icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        gradient: 'from-purple-500 to-pink-600'\n    },\n    {\n        name: 'Flexible Scheduling',\n        description: 'Smart scheduling that adapts to your timezone and availability preferences for stress-free coordination.',\n        icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        gradient: 'from-orange-500 to-red-600'\n    },\n    {\n        name: 'Global Community',\n        description: 'Connect with skill partners worldwide or find local experts in your area for in-person exchanges.',\n        icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        gradient: 'from-indigo-500 to-blue-600'\n    },\n    {\n        name: 'Verified Skills',\n        description: 'Community-driven skill verification and rating system ensures quality and builds trust.',\n        icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        gradient: 'from-teal-500 to-green-600'\n    },\n    {\n        name: 'Smart Recommendations',\n        description: 'Get personalized skill suggestions based on your interests, career goals, and learning history.',\n        icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        gradient: 'from-yellow-500 to-orange-600'\n    },\n    {\n        name: 'Quality Assurance',\n        description: 'Built-in rating system and feedback mechanism ensure high-quality skill exchanges every time.',\n        icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        gradient: 'from-pink-500 to-purple-600'\n    }\n];\nfunction FeaturesSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-7xl px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto max-w-2xl text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                            children: \"Everything you need for successful skill exchanges\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"mt-6 text-lg leading-8 text-gray-600\",\n                            children: \"From intelligent matching to secure communication, we've built every feature with one goal: making skill sharing effortless and enjoyable.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto mt-16 max-w-7xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 40\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"group relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative overflow-hidden rounded-2xl bg-white p-8 shadow-sm border border-gray-200 h-full transition-all duration-300 hover:shadow-lg hover:scale-105\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br \".concat(feature.gradient, \" opacity-0 group-hover:opacity-5 transition-opacity duration-300\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br \".concat(feature.gradient, \" shadow-lg\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"h-6 w-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 group-hover:text-gray-700 transition-colors\",\n                                                    children: feature.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-3 text-gray-600 leading-relaxed\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 w-2 h-2 bg-gray-200 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 17\n                                }, this)\n                            }, feature.name, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.6,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mx-auto mt-16 max-w-2xl text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-2xl bg-gradient-to-br from-blue-50 to-purple-50 p-8 border border-blue-100\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Ready to experience the future of skill sharing?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Join thousands of learners who are already expanding their skills through meaningful connections.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-3 text-white font-semibold shadow-lg hover:scale-105 transition-transform duration-200\",\n                                children: [\n                                    \"Get Started Free\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ClockIcon_GlobeAltIcon_LightBulbIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\features-section.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_c = FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/features-section.tsx\n"));

/***/ })

});