{"version": 3, "names": ["<PERSON><PERSON><PERSON>", "constructor", "map", "indentChar", "_map", "_buf", "_str", "_appendCount", "_last", "_queue", "_queueCursor", "_canMarkIdName", "_indentChar", "_fastIndentations", "_position", "line", "column", "_sourcePosition", "identifierName", "undefined", "identifierNamePos", "filename", "i", "push", "repeat", "_allocQueue", "queue", "char", "_pushQueue", "cursor", "length", "item", "_popQueue", "Error", "get", "_flush", "result", "code", "trimRight", "decodedMap", "getDecoded", "__mergedMap", "resultMap", "value", "Object", "defineProperty", "writable", "rawMappings", "mappings", "getRawMappings", "append", "str", "maybeNewline", "_append", "appendChar", "_appendChar", "sourcePosition", "queueIndentation", "queueCursor", "sourcePos", "fastIndentation", "String", "fromCharCode", "_mark", "len", "position", "charCodeAt", "indexOf", "last", "_this$_map", "mark", "removeTrailingNewline", "removeLastSemicolon", "getLastChar", "getNewlineCount", "count", "endsWithCharAndNewline", "lastCp", "<PERSON><PERSON><PERSON><PERSON>", "exactSource", "loc", "cb", "source", "prop", "_normalizePosition", "sourceWithOffset", "columnOffset", "pos", "target", "Math", "max", "getCurrentColumn", "lastIndex", "getCurrentLine", "exports", "default"], "sources": ["../src/buffer.ts"], "sourcesContent": ["import type SourceMap from \"./source-map.ts\";\n\n// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charcodes from \"charcodes\";\n\nexport type Pos = {\n  line: number;\n  column: number;\n  index: number;\n};\nexport type Loc = {\n  start?: Pos;\n  end?: Pos;\n  filename?: string;\n};\ntype SourcePos = {\n  line: number | undefined;\n  column: number | undefined;\n  identifierName: string | undefined;\n  filename: string | undefined;\n};\ntype InternalSourcePos = SourcePos & { identifierNamePos: Pos };\n\ntype QueueItem = {\n  char: number;\n  repeat: number;\n  line: number | undefined;\n  column: number | undefined;\n  identifierName: undefined; // Not used, it always undefined.\n  identifierNamePos: undefined; // Not used, it always undefined.\n  filename: string | undefined;\n};\n\nexport default class Buffer {\n  constructor(map: SourceMap | null, indentChar: string) {\n    this._map = map;\n    this._indentChar = indentChar;\n\n    for (let i = 0; i < 64; i++) {\n      this._fastIndentations.push(indentChar.repeat(i));\n    }\n\n    this._allocQueue();\n  }\n\n  _map: SourceMap = null;\n  _buf = \"\";\n  _str = \"\";\n  _appendCount = 0;\n  _last = 0;\n  _queue: QueueItem[] = [];\n  _queueCursor = 0;\n  _canMarkIdName = true;\n  _indentChar = \"\";\n  _fastIndentations: string[] = [];\n\n  _position = {\n    line: 1,\n    column: 0,\n  };\n  _sourcePosition: InternalSourcePos = {\n    identifierName: undefined,\n    identifierNamePos: undefined,\n    line: undefined,\n    column: undefined,\n    filename: undefined,\n  };\n\n  _allocQueue() {\n    const queue = this._queue;\n\n    for (let i = 0; i < 16; i++) {\n      queue.push({\n        char: 0,\n        repeat: 1,\n        line: undefined,\n        column: undefined,\n        identifierName: undefined,\n        identifierNamePos: undefined,\n        filename: \"\",\n      });\n    }\n  }\n\n  _pushQueue(\n    char: number,\n    repeat: number,\n    line: number | undefined,\n    column: number | undefined,\n    filename: string | undefined,\n  ) {\n    const cursor = this._queueCursor;\n    if (cursor === this._queue.length) {\n      this._allocQueue();\n    }\n    const item = this._queue[cursor];\n    item.char = char;\n    item.repeat = repeat;\n    item.line = line;\n    item.column = column;\n    item.filename = filename;\n\n    this._queueCursor++;\n  }\n\n  _popQueue(): QueueItem {\n    if (this._queueCursor === 0) {\n      throw new Error(\"Cannot pop from empty queue\");\n    }\n    return this._queue[--this._queueCursor];\n  }\n\n  /**\n   * Get the final string output from the buffer, along with the sourcemap if one exists.\n   */\n\n  get() {\n    this._flush();\n\n    const map = this._map;\n    const result = {\n      // Whatever trim is used here should not execute a regex against the\n      // source string since it may be arbitrarily large after all transformations\n      code: (this._buf + this._str).trimRight(),\n      // Decoded sourcemap is free to generate.\n      decodedMap: map?.getDecoded(),\n      // Used as a marker for backwards compatibility. We moved input map merging\n      // into the generator. We cannot merge the input map a second time, so the\n      // presence of this field tells us we've already done the work.\n      get __mergedMap() {\n        return this.map;\n      },\n      // Encoding the sourcemap is moderately CPU expensive.\n      get map() {\n        const resultMap = map ? map.get() : null;\n        result.map = resultMap;\n        return resultMap;\n      },\n      set map(value) {\n        Object.defineProperty(result, \"map\", { value, writable: true });\n      },\n      // Retrieving the raw mappings is very memory intensive.\n      get rawMappings() {\n        const mappings = map?.getRawMappings();\n        result.rawMappings = mappings;\n        return mappings;\n      },\n      set rawMappings(value) {\n        Object.defineProperty(result, \"rawMappings\", { value, writable: true });\n      },\n    };\n\n    return result;\n  }\n\n  /**\n   * Add a string to the buffer that cannot be reverted.\n   */\n\n  append(str: string, maybeNewline: boolean): void {\n    this._flush();\n\n    this._append(str, this._sourcePosition, maybeNewline);\n  }\n\n  appendChar(char: number): void {\n    this._flush();\n    this._appendChar(char, 1, this._sourcePosition);\n  }\n\n  /**\n   * Add a string to the buffer than can be reverted.\n   */\n  queue(char: number): void {\n    // Drop trailing spaces when a newline is inserted.\n    if (char === charcodes.lineFeed) {\n      while (this._queueCursor !== 0) {\n        const char = this._queue[this._queueCursor - 1].char;\n        if (char !== charcodes.space && char !== charcodes.tab) {\n          break;\n        }\n\n        this._queueCursor--;\n      }\n    }\n\n    const sourcePosition = this._sourcePosition;\n    this._pushQueue(\n      char,\n      1,\n      sourcePosition.line,\n      sourcePosition.column,\n      sourcePosition.filename,\n    );\n  }\n\n  /**\n   * Same as queue, but this indentation will never have a sourcemap marker.\n   */\n  queueIndentation(repeat: number): void {\n    if (repeat === 0) return;\n    this._pushQueue(-1, repeat, undefined, undefined, undefined);\n  }\n\n  _flush(): void {\n    const queueCursor = this._queueCursor;\n    const queue = this._queue;\n    for (let i = 0; i < queueCursor; i++) {\n      const item: QueueItem = queue[i];\n      this._appendChar(item.char, item.repeat, item);\n    }\n    this._queueCursor = 0;\n  }\n\n  _appendChar(\n    char: number,\n    repeat: number,\n    sourcePos: InternalSourcePos,\n  ): void {\n    this._last = char;\n\n    if (char === -1) {\n      const fastIndentation = this._fastIndentations[repeat];\n      if (fastIndentation !== undefined) {\n        this._str += fastIndentation;\n      } else {\n        this._str +=\n          repeat > 1 ? this._indentChar.repeat(repeat) : this._indentChar;\n      }\n    } else {\n      this._str +=\n        repeat > 1\n          ? String.fromCharCode(char).repeat(repeat)\n          : String.fromCharCode(char);\n    }\n\n    if (char !== charcodes.lineFeed) {\n      this._mark(\n        sourcePos.line,\n        sourcePos.column,\n        sourcePos.identifierName,\n        sourcePos.identifierNamePos,\n        sourcePos.filename,\n      );\n      this._position.column += repeat;\n    } else {\n      this._position.line++;\n      this._position.column = 0;\n    }\n\n    if (this._canMarkIdName) {\n      sourcePos.identifierName = undefined;\n      sourcePos.identifierNamePos = undefined;\n    }\n  }\n\n  _append(\n    str: string,\n    sourcePos: InternalSourcePos,\n    maybeNewline: boolean,\n  ): void {\n    const len = str.length;\n    const position = this._position;\n\n    this._last = str.charCodeAt(len - 1);\n\n    if (++this._appendCount > 4096) {\n      // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n      +this._str; // Unexplainable huge performance boost. Ref: https://github.com/davidmarkclements/flatstr License: MIT\n      this._buf += this._str;\n      this._str = str;\n      this._appendCount = 0;\n    } else {\n      this._str += str;\n    }\n\n    if (!maybeNewline && !this._map) {\n      position.column += len;\n      return;\n    }\n\n    const { column, identifierName, identifierNamePos, filename } = sourcePos;\n    let line = sourcePos.line;\n\n    if (\n      (identifierName != null || identifierNamePos != null) &&\n      this._canMarkIdName\n    ) {\n      sourcePos.identifierName = undefined;\n      sourcePos.identifierNamePos = undefined;\n    }\n\n    // Search for newline chars. We search only for `\\n`, since both `\\r` and\n    // `\\r\\n` are normalized to `\\n` during parse. We exclude `\\u2028` and\n    // `\\u2029` for performance reasons, they're so uncommon that it's probably\n    // ok. It's also unclear how other sourcemap utilities handle them...\n    let i = str.indexOf(\"\\n\");\n    let last = 0;\n\n    // If the string starts with a newline char, then adding a mark is redundant.\n    // This catches both \"no newlines\" and \"newline after several chars\".\n    if (i !== 0) {\n      this._mark(line, column, identifierName, identifierNamePos, filename);\n    }\n\n    // Now, find each remaining newline char in the string.\n    while (i !== -1) {\n      position.line++;\n      position.column = 0;\n      last = i + 1;\n\n      // We mark the start of each line, which happens directly after this newline char\n      // unless this is the last char.\n      // When manually adding multi-line content (such as a comment), `line` will be `undefined`.\n      if (last < len && line !== undefined) {\n        this._mark(++line, 0, null, null, filename);\n      }\n      i = str.indexOf(\"\\n\", last);\n    }\n    position.column += len - last;\n  }\n\n  _mark(\n    line: number | undefined,\n    column: number | undefined,\n    identifierName: string | undefined,\n    identifierNamePos: Pos | undefined,\n    filename: string | undefined,\n  ): void {\n    this._map?.mark(\n      this._position,\n      line,\n      column,\n      identifierName,\n      identifierNamePos,\n      filename,\n    );\n  }\n\n  removeTrailingNewline(): void {\n    const queueCursor = this._queueCursor;\n    if (\n      queueCursor !== 0 &&\n      this._queue[queueCursor - 1].char === charcodes.lineFeed\n    ) {\n      this._queueCursor--;\n    }\n  }\n\n  removeLastSemicolon(): void {\n    const queueCursor = this._queueCursor;\n    if (\n      queueCursor !== 0 &&\n      this._queue[queueCursor - 1].char === charcodes.semicolon\n    ) {\n      this._queueCursor--;\n    }\n  }\n\n  getLastChar(): number {\n    const queueCursor = this._queueCursor;\n    return queueCursor !== 0 ? this._queue[queueCursor - 1].char : this._last;\n  }\n\n  /**\n   * This will only detect at most 1 newline after a call to `flush()`,\n   * but this has not been found so far, and an accurate count can be achieved if needed later.\n   */\n  getNewlineCount(): number {\n    const queueCursor = this._queueCursor;\n    let count = 0;\n    if (queueCursor === 0) return this._last === charcodes.lineFeed ? 1 : 0;\n    for (let i = queueCursor - 1; i >= 0; i--) {\n      if (this._queue[i].char !== charcodes.lineFeed) {\n        break;\n      }\n      count++;\n    }\n    return count === queueCursor && this._last === charcodes.lineFeed\n      ? count + 1\n      : count;\n  }\n\n  /**\n   * check if current _last + queue ends with newline, return the character before newline\n   */\n  endsWithCharAndNewline(): number {\n    const queue = this._queue;\n    const queueCursor = this._queueCursor;\n    if (queueCursor !== 0) {\n      // every element in queue is one-length whitespace string\n      const lastCp = queue[queueCursor - 1].char;\n      if (lastCp !== charcodes.lineFeed) return;\n      if (queueCursor > 1) {\n        return queue[queueCursor - 2].char;\n      } else {\n        return this._last;\n      }\n    }\n    // We assume that everything being matched is at most a single token plus some whitespace,\n    // which everything currently is, but otherwise we'd have to expand _last or check _buf.\n  }\n\n  hasContent(): boolean {\n    return this._queueCursor !== 0 || !!this._last;\n  }\n\n  /**\n   * Certain sourcemap usecases expect mappings to be more accurate than\n   * Babel's generic sourcemap handling allows. For now, we special-case\n   * identifiers to allow for the primary cases to work.\n   * The goal of this line is to ensure that the map output from Babel will\n   * have an exact range on identifiers in the output code. Without this\n   * line, Babel would potentially include some number of trailing tokens\n   * that are printed after the identifier, but before another location has\n   * been assigned.\n   * This allows tooling like Rollup and Webpack to more accurately perform\n   * their own transformations. Most importantly, this allows the import/export\n   * transformations performed by those tools to loose less information when\n   * applying their own transformations on top of the code and map results\n   * generated by Babel itself.\n   *\n   * The primary example of this is the snippet:\n   *\n   *   import mod from \"mod\";\n   *   mod();\n   *\n   * With this line, there will be one mapping range over \"mod\" and another\n   * over \"();\", where previously it would have been a single mapping.\n   */\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!this._map) {\n      cb();\n      return;\n    }\n\n    this.source(\"start\", loc);\n    // @ts-expect-error identifierName is not defined\n    const identifierName = loc.identifierName;\n    const sourcePos = this._sourcePosition;\n    if (identifierName) {\n      this._canMarkIdName = false;\n      sourcePos.identifierName = identifierName;\n    }\n    cb();\n\n    if (identifierName) {\n      this._canMarkIdName = true;\n      sourcePos.identifierName = undefined;\n      sourcePos.identifierNamePos = undefined;\n    }\n    this.source(\"end\", loc);\n  }\n\n  /**\n   * Sets a given position as the current source location so generated code after this call\n   * will be given this position in the sourcemap.\n   */\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!this._map) return;\n\n    // Since this is called extremely often, we reuse the same _sourcePosition\n    // object for the whole lifetime of the buffer.\n    this._normalizePosition(prop, loc, 0);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    columnOffset: number,\n  ): void {\n    if (!this._map) return;\n\n    this._normalizePosition(prop, loc, columnOffset);\n  }\n\n  _normalizePosition(prop: \"start\" | \"end\", loc: Loc, columnOffset: number) {\n    const pos = loc[prop];\n    const target = this._sourcePosition;\n\n    if (pos) {\n      target.line = pos.line;\n      // TODO: Fix https://github.com/babel/babel/issues/15712 in downstream\n      target.column = Math.max(pos.column + columnOffset, 0);\n      target.filename = loc.filename;\n    }\n  }\n\n  getCurrentColumn(): number {\n    const queue = this._queue;\n    const queueCursor = this._queueCursor;\n\n    let lastIndex = -1;\n    let len = 0;\n    for (let i = 0; i < queueCursor; i++) {\n      const item = queue[i];\n      if (item.char === charcodes.lineFeed) {\n        lastIndex = len;\n      }\n      len += item.repeat;\n    }\n\n    return lastIndex === -1 ? this._position.column + len : len - 1 - lastIndex;\n  }\n\n  getCurrentLine(): number {\n    let count = 0;\n\n    const queue = this._queue;\n    for (let i = 0; i < this._queueCursor; i++) {\n      if (queue[i].char === charcodes.lineFeed) {\n        count++;\n      }\n    }\n\n    return this._position.line + count;\n  }\n}\n"], "mappings": ";;;;;;AAkCe,MAAMA,MAAM,CAAC;EAC1BC,WAAWA,CAACC,GAAqB,EAAEC,UAAkB,EAAE;IAAA,KAWvDC,IAAI,GAAc,IAAI;IAAA,KACtBC,IAAI,GAAG,EAAE;IAAA,KACTC,IAAI,GAAG,EAAE;IAAA,KACTC,YAAY,GAAG,CAAC;IAAA,KAChBC,KAAK,GAAG,CAAC;IAAA,KACTC,MAAM,GAAgB,EAAE;IAAA,KACxBC,YAAY,GAAG,CAAC;IAAA,KAChBC,cAAc,GAAG,IAAI;IAAA,KACrBC,WAAW,GAAG,EAAE;IAAA,KAChBC,iBAAiB,GAAa,EAAE;IAAA,KAEhCC,SAAS,GAAG;MACVC,IAAI,EAAE,CAAC;MACPC,MAAM,EAAE;IACV,CAAC;IAAA,KACDC,eAAe,GAAsB;MACnCC,cAAc,EAAEC,SAAS;MACzBC,iBAAiB,EAAED,SAAS;MAC5BJ,IAAI,EAAEI,SAAS;MACfH,MAAM,EAAEG,SAAS;MACjBE,QAAQ,EAAEF;IACZ,CAAC;IA/BC,IAAI,CAACf,IAAI,GAAGF,GAAG;IACf,IAAI,CAACU,WAAW,GAAGT,UAAU;IAE7B,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,IAAI,CAACT,iBAAiB,CAACU,IAAI,CAACpB,UAAU,CAACqB,MAAM,CAACF,CAAC,CAAC,CAAC;IACnD;IAEA,IAAI,CAACG,WAAW,CAAC,CAAC;EACpB;EAyBAA,WAAWA,CAAA,EAAG;IACZ,MAAMC,KAAK,GAAG,IAAI,CAACjB,MAAM;IAEzB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3BI,KAAK,CAACH,IAAI,CAAC;QACTI,IAAI,EAAE,CAAC;QACPH,MAAM,EAAE,CAAC;QACTT,IAAI,EAAEI,SAAS;QACfH,MAAM,EAAEG,SAAS;QACjBD,cAAc,EAAEC,SAAS;QACzBC,iBAAiB,EAAED,SAAS;QAC5BE,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF;EAEAO,UAAUA,CACRD,IAAY,EACZH,MAAc,EACdT,IAAwB,EACxBC,MAA0B,EAC1BK,QAA4B,EAC5B;IACA,MAAMQ,MAAM,GAAG,IAAI,CAACnB,YAAY;IAChC,IAAImB,MAAM,KAAK,IAAI,CAACpB,MAAM,CAACqB,MAAM,EAAE;MACjC,IAAI,CAACL,WAAW,CAAC,CAAC;IACpB;IACA,MAAMM,IAAI,GAAG,IAAI,CAACtB,MAAM,CAACoB,MAAM,CAAC;IAChCE,IAAI,CAACJ,IAAI,GAAGA,IAAI;IAChBI,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpBO,IAAI,CAAChB,IAAI,GAAGA,IAAI;IAChBgB,IAAI,CAACf,MAAM,GAAGA,MAAM;IACpBe,IAAI,CAACV,QAAQ,GAAGA,QAAQ;IAExB,IAAI,CAACX,YAAY,EAAE;EACrB;EAEAsB,SAASA,CAAA,EAAc;IACrB,IAAI,IAAI,CAACtB,YAAY,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIuB,KAAK,CAAC,6BAA6B,CAAC;IAChD;IACA,OAAO,IAAI,CAACxB,MAAM,CAAC,EAAE,IAAI,CAACC,YAAY,CAAC;EACzC;EAMAwB,GAAGA,CAAA,EAAG;IACJ,IAAI,CAACC,MAAM,CAAC,CAAC;IAEb,MAAMjC,GAAG,GAAG,IAAI,CAACE,IAAI;IACrB,MAAMgC,MAAM,GAAG;MAGbC,IAAI,EAAE,CAAC,IAAI,CAAChC,IAAI,GAAG,IAAI,CAACC,IAAI,EAAEgC,SAAS,CAAC,CAAC;MAEzCC,UAAU,EAAErC,GAAG,oBAAHA,GAAG,CAAEsC,UAAU,CAAC,CAAC;MAI7B,IAAIC,WAAWA,CAAA,EAAG;QAChB,OAAO,IAAI,CAACvC,GAAG;MACjB,CAAC;MAED,IAAIA,GAAGA,CAAA,EAAG;QACR,MAAMwC,SAAS,GAAGxC,GAAG,GAAGA,GAAG,CAACgC,GAAG,CAAC,CAAC,GAAG,IAAI;QACxCE,MAAM,CAAClC,GAAG,GAAGwC,SAAS;QACtB,OAAOA,SAAS;MAClB,CAAC;MACD,IAAIxC,GAAGA,CAACyC,KAAK,EAAE;QACbC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAE,KAAK,EAAE;UAAEO,KAAK;UAAEG,QAAQ,EAAE;QAAK,CAAC,CAAC;MACjE,CAAC;MAED,IAAIC,WAAWA,CAAA,EAAG;QAChB,MAAMC,QAAQ,GAAG9C,GAAG,oBAAHA,GAAG,CAAE+C,cAAc,CAAC,CAAC;QACtCb,MAAM,CAACW,WAAW,GAAGC,QAAQ;QAC7B,OAAOA,QAAQ;MACjB,CAAC;MACD,IAAID,WAAWA,CAACJ,KAAK,EAAE;QACrBC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAE,aAAa,EAAE;UAAEO,KAAK;UAAEG,QAAQ,EAAE;QAAK,CAAC,CAAC;MACzE;IACF,CAAC;IAED,OAAOV,MAAM;EACf;EAMAc,MAAMA,CAACC,GAAW,EAAEC,YAAqB,EAAQ;IAC/C,IAAI,CAACjB,MAAM,CAAC,CAAC;IAEb,IAAI,CAACkB,OAAO,CAACF,GAAG,EAAE,IAAI,CAAClC,eAAe,EAAEmC,YAAY,CAAC;EACvD;EAEAE,UAAUA,CAAC3B,IAAY,EAAQ;IAC7B,IAAI,CAACQ,MAAM,CAAC,CAAC;IACb,IAAI,CAACoB,WAAW,CAAC5B,IAAI,EAAE,CAAC,EAAE,IAAI,CAACV,eAAe,CAAC;EACjD;EAKAS,KAAKA,CAACC,IAAY,EAAQ;IAExB,IAAIA,IAAI,OAAuB,EAAE;MAC/B,OAAO,IAAI,CAACjB,YAAY,KAAK,CAAC,EAAE;QAC9B,MAAMiB,IAAI,GAAG,IAAI,CAAClB,MAAM,CAAC,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC,CAACiB,IAAI;QACpD,IAAIA,IAAI,OAAoB,IAAIA,IAAI,MAAkB,EAAE;UACtD;QACF;QAEA,IAAI,CAACjB,YAAY,EAAE;MACrB;IACF;IAEA,MAAM8C,cAAc,GAAG,IAAI,CAACvC,eAAe;IAC3C,IAAI,CAACW,UAAU,CACbD,IAAI,EACJ,CAAC,EACD6B,cAAc,CAACzC,IAAI,EACnByC,cAAc,CAACxC,MAAM,EACrBwC,cAAc,CAACnC,QACjB,CAAC;EACH;EAKAoC,gBAAgBA,CAACjC,MAAc,EAAQ;IACrC,IAAIA,MAAM,KAAK,CAAC,EAAE;IAClB,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,EAAEJ,MAAM,EAAEL,SAAS,EAAEA,SAAS,EAAEA,SAAS,CAAC;EAC9D;EAEAgB,MAAMA,CAAA,EAAS;IACb,MAAMuB,WAAW,GAAG,IAAI,CAAChD,YAAY;IACrC,MAAMgB,KAAK,GAAG,IAAI,CAACjB,MAAM;IACzB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,WAAW,EAAEpC,CAAC,EAAE,EAAE;MACpC,MAAMS,IAAe,GAAGL,KAAK,CAACJ,CAAC,CAAC;MAChC,IAAI,CAACiC,WAAW,CAACxB,IAAI,CAACJ,IAAI,EAAEI,IAAI,CAACP,MAAM,EAAEO,IAAI,CAAC;IAChD;IACA,IAAI,CAACrB,YAAY,GAAG,CAAC;EACvB;EAEA6C,WAAWA,CACT5B,IAAY,EACZH,MAAc,EACdmC,SAA4B,EACtB;IACN,IAAI,CAACnD,KAAK,GAAGmB,IAAI;IAEjB,IAAIA,IAAI,KAAK,CAAC,CAAC,EAAE;MACf,MAAMiC,eAAe,GAAG,IAAI,CAAC/C,iBAAiB,CAACW,MAAM,CAAC;MACtD,IAAIoC,eAAe,KAAKzC,SAAS,EAAE;QACjC,IAAI,CAACb,IAAI,IAAIsD,eAAe;MAC9B,CAAC,MAAM;QACL,IAAI,CAACtD,IAAI,IACPkB,MAAM,GAAG,CAAC,GAAG,IAAI,CAACZ,WAAW,CAACY,MAAM,CAACA,MAAM,CAAC,GAAG,IAAI,CAACZ,WAAW;MACnE;IACF,CAAC,MAAM;MACL,IAAI,CAACN,IAAI,IACPkB,MAAM,GAAG,CAAC,GACNqC,MAAM,CAACC,YAAY,CAACnC,IAAI,CAAC,CAACH,MAAM,CAACA,MAAM,CAAC,GACxCqC,MAAM,CAACC,YAAY,CAACnC,IAAI,CAAC;IACjC;IAEA,IAAIA,IAAI,OAAuB,EAAE;MAC/B,IAAI,CAACoC,KAAK,CACRJ,SAAS,CAAC5C,IAAI,EACd4C,SAAS,CAAC3C,MAAM,EAChB2C,SAAS,CAACzC,cAAc,EACxByC,SAAS,CAACvC,iBAAiB,EAC3BuC,SAAS,CAACtC,QACZ,CAAC;MACD,IAAI,CAACP,SAAS,CAACE,MAAM,IAAIQ,MAAM;IACjC,CAAC,MAAM;MACL,IAAI,CAACV,SAAS,CAACC,IAAI,EAAE;MACrB,IAAI,CAACD,SAAS,CAACE,MAAM,GAAG,CAAC;IAC3B;IAEA,IAAI,IAAI,CAACL,cAAc,EAAE;MACvBgD,SAAS,CAACzC,cAAc,GAAGC,SAAS;MACpCwC,SAAS,CAACvC,iBAAiB,GAAGD,SAAS;IACzC;EACF;EAEAkC,OAAOA,CACLF,GAAW,EACXQ,SAA4B,EAC5BP,YAAqB,EACf;IACN,MAAMY,GAAG,GAAGb,GAAG,CAACrB,MAAM;IACtB,MAAMmC,QAAQ,GAAG,IAAI,CAACnD,SAAS;IAE/B,IAAI,CAACN,KAAK,GAAG2C,GAAG,CAACe,UAAU,CAACF,GAAG,GAAG,CAAC,CAAC;IAEpC,IAAI,EAAE,IAAI,CAACzD,YAAY,GAAG,IAAI,EAAE;MAE9B,CAAC,IAAI,CAACD,IAAI;MACV,IAAI,CAACD,IAAI,IAAI,IAAI,CAACC,IAAI;MACtB,IAAI,CAACA,IAAI,GAAG6C,GAAG;MACf,IAAI,CAAC5C,YAAY,GAAG,CAAC;IACvB,CAAC,MAAM;MACL,IAAI,CAACD,IAAI,IAAI6C,GAAG;IAClB;IAEA,IAAI,CAACC,YAAY,IAAI,CAAC,IAAI,CAAChD,IAAI,EAAE;MAC/B6D,QAAQ,CAACjD,MAAM,IAAIgD,GAAG;MACtB;IACF;IAEA,MAAM;MAAEhD,MAAM;MAAEE,cAAc;MAAEE,iBAAiB;MAAEC;IAAS,CAAC,GAAGsC,SAAS;IACzE,IAAI5C,IAAI,GAAG4C,SAAS,CAAC5C,IAAI;IAEzB,IACE,CAACG,cAAc,IAAI,IAAI,IAAIE,iBAAiB,IAAI,IAAI,KACpD,IAAI,CAACT,cAAc,EACnB;MACAgD,SAAS,CAACzC,cAAc,GAAGC,SAAS;MACpCwC,SAAS,CAACvC,iBAAiB,GAAGD,SAAS;IACzC;IAMA,IAAIG,CAAC,GAAG6B,GAAG,CAACgB,OAAO,CAAC,IAAI,CAAC;IACzB,IAAIC,IAAI,GAAG,CAAC;IAIZ,IAAI9C,CAAC,KAAK,CAAC,EAAE;MACX,IAAI,CAACyC,KAAK,CAAChD,IAAI,EAAEC,MAAM,EAAEE,cAAc,EAAEE,iBAAiB,EAAEC,QAAQ,CAAC;IACvE;IAGA,OAAOC,CAAC,KAAK,CAAC,CAAC,EAAE;MACf2C,QAAQ,CAAClD,IAAI,EAAE;MACfkD,QAAQ,CAACjD,MAAM,GAAG,CAAC;MACnBoD,IAAI,GAAG9C,CAAC,GAAG,CAAC;MAKZ,IAAI8C,IAAI,GAAGJ,GAAG,IAAIjD,IAAI,KAAKI,SAAS,EAAE;QACpC,IAAI,CAAC4C,KAAK,CAAC,EAAEhD,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAEM,QAAQ,CAAC;MAC7C;MACAC,CAAC,GAAG6B,GAAG,CAACgB,OAAO,CAAC,IAAI,EAAEC,IAAI,CAAC;IAC7B;IACAH,QAAQ,CAACjD,MAAM,IAAIgD,GAAG,GAAGI,IAAI;EAC/B;EAEAL,KAAKA,CACHhD,IAAwB,EACxBC,MAA0B,EAC1BE,cAAkC,EAClCE,iBAAkC,EAClCC,QAA4B,EACtB;IAAA,IAAAgD,UAAA;IACN,CAAAA,UAAA,OAAI,CAACjE,IAAI,aAATiE,UAAA,CAAWC,IAAI,CACb,IAAI,CAACxD,SAAS,EACdC,IAAI,EACJC,MAAM,EACNE,cAAc,EACdE,iBAAiB,EACjBC,QACF,CAAC;EACH;EAEAkD,qBAAqBA,CAAA,EAAS;IAC5B,MAAMb,WAAW,GAAG,IAAI,CAAChD,YAAY;IACrC,IACEgD,WAAW,KAAK,CAAC,IACjB,IAAI,CAACjD,MAAM,CAACiD,WAAW,GAAG,CAAC,CAAC,CAAC/B,IAAI,OAAuB,EACxD;MACA,IAAI,CAACjB,YAAY,EAAE;IACrB;EACF;EAEA8D,mBAAmBA,CAAA,EAAS;IAC1B,MAAMd,WAAW,GAAG,IAAI,CAAChD,YAAY;IACrC,IACEgD,WAAW,KAAK,CAAC,IACjB,IAAI,CAACjD,MAAM,CAACiD,WAAW,GAAG,CAAC,CAAC,CAAC/B,IAAI,OAAwB,EACzD;MACA,IAAI,CAACjB,YAAY,EAAE;IACrB;EACF;EAEA+D,WAAWA,CAAA,EAAW;IACpB,MAAMf,WAAW,GAAG,IAAI,CAAChD,YAAY;IACrC,OAAOgD,WAAW,KAAK,CAAC,GAAG,IAAI,CAACjD,MAAM,CAACiD,WAAW,GAAG,CAAC,CAAC,CAAC/B,IAAI,GAAG,IAAI,CAACnB,KAAK;EAC3E;EAMAkE,eAAeA,CAAA,EAAW;IACxB,MAAMhB,WAAW,GAAG,IAAI,CAAChD,YAAY;IACrC,IAAIiE,KAAK,GAAG,CAAC;IACb,IAAIjB,WAAW,KAAK,CAAC,EAAE,OAAO,IAAI,CAAClD,KAAK,OAAuB,GAAG,CAAC,GAAG,CAAC;IACvE,KAAK,IAAIc,CAAC,GAAGoC,WAAW,GAAG,CAAC,EAAEpC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,IAAI,IAAI,CAACb,MAAM,CAACa,CAAC,CAAC,CAACK,IAAI,OAAuB,EAAE;QAC9C;MACF;MACAgD,KAAK,EAAE;IACT;IACA,OAAOA,KAAK,KAAKjB,WAAW,IAAI,IAAI,CAAClD,KAAK,OAAuB,GAC7DmE,KAAK,GAAG,CAAC,GACTA,KAAK;EACX;EAKAC,sBAAsBA,CAAA,EAAW;IAC/B,MAAMlD,KAAK,GAAG,IAAI,CAACjB,MAAM;IACzB,MAAMiD,WAAW,GAAG,IAAI,CAAChD,YAAY;IACrC,IAAIgD,WAAW,KAAK,CAAC,EAAE;MAErB,MAAMmB,MAAM,GAAGnD,KAAK,CAACgC,WAAW,GAAG,CAAC,CAAC,CAAC/B,IAAI;MAC1C,IAAIkD,MAAM,OAAuB,EAAE;MACnC,IAAInB,WAAW,GAAG,CAAC,EAAE;QACnB,OAAOhC,KAAK,CAACgC,WAAW,GAAG,CAAC,CAAC,CAAC/B,IAAI;MACpC,CAAC,MAAM;QACL,OAAO,IAAI,CAACnB,KAAK;MACnB;IACF;EAGF;EAEAsE,UAAUA,CAAA,EAAY;IACpB,OAAO,IAAI,CAACpE,YAAY,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAACF,KAAK;EAChD;EAyBAuE,WAAWA,CAACC,GAAoB,EAAEC,EAAc,EAAE;IAChD,IAAI,CAAC,IAAI,CAAC7E,IAAI,EAAE;MACd6E,EAAE,CAAC,CAAC;MACJ;IACF;IAEA,IAAI,CAACC,MAAM,CAAC,OAAO,EAAEF,GAAG,CAAC;IAEzB,MAAM9D,cAAc,GAAG8D,GAAG,CAAC9D,cAAc;IACzC,MAAMyC,SAAS,GAAG,IAAI,CAAC1C,eAAe;IACtC,IAAIC,cAAc,EAAE;MAClB,IAAI,CAACP,cAAc,GAAG,KAAK;MAC3BgD,SAAS,CAACzC,cAAc,GAAGA,cAAc;IAC3C;IACA+D,EAAE,CAAC,CAAC;IAEJ,IAAI/D,cAAc,EAAE;MAClB,IAAI,CAACP,cAAc,GAAG,IAAI;MAC1BgD,SAAS,CAACzC,cAAc,GAAGC,SAAS;MACpCwC,SAAS,CAACvC,iBAAiB,GAAGD,SAAS;IACzC;IACA,IAAI,CAAC+D,MAAM,CAAC,KAAK,EAAEF,GAAG,CAAC;EACzB;EAOAE,MAAMA,CAACC,IAAqB,EAAEH,GAAoB,EAAQ;IACxD,IAAI,CAAC,IAAI,CAAC5E,IAAI,EAAE;IAIhB,IAAI,CAACgF,kBAAkB,CAACD,IAAI,EAAEH,GAAG,EAAE,CAAC,CAAC;EACvC;EAEAK,gBAAgBA,CACdF,IAAqB,EACrBH,GAAoB,EACpBM,YAAoB,EACd;IACN,IAAI,CAAC,IAAI,CAAClF,IAAI,EAAE;IAEhB,IAAI,CAACgF,kBAAkB,CAACD,IAAI,EAAEH,GAAG,EAAEM,YAAY,CAAC;EAClD;EAEAF,kBAAkBA,CAACD,IAAqB,EAAEH,GAAQ,EAAEM,YAAoB,EAAE;IACxE,MAAMC,GAAG,GAAGP,GAAG,CAACG,IAAI,CAAC;IACrB,MAAMK,MAAM,GAAG,IAAI,CAACvE,eAAe;IAEnC,IAAIsE,GAAG,EAAE;MACPC,MAAM,CAACzE,IAAI,GAAGwE,GAAG,CAACxE,IAAI;MAEtByE,MAAM,CAACxE,MAAM,GAAGyE,IAAI,CAACC,GAAG,CAACH,GAAG,CAACvE,MAAM,GAAGsE,YAAY,EAAE,CAAC,CAAC;MACtDE,MAAM,CAACnE,QAAQ,GAAG2D,GAAG,CAAC3D,QAAQ;IAChC;EACF;EAEAsE,gBAAgBA,CAAA,EAAW;IACzB,MAAMjE,KAAK,GAAG,IAAI,CAACjB,MAAM;IACzB,MAAMiD,WAAW,GAAG,IAAI,CAAChD,YAAY;IAErC,IAAIkF,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI5B,GAAG,GAAG,CAAC;IACX,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,WAAW,EAAEpC,CAAC,EAAE,EAAE;MACpC,MAAMS,IAAI,GAAGL,KAAK,CAACJ,CAAC,CAAC;MACrB,IAAIS,IAAI,CAACJ,IAAI,OAAuB,EAAE;QACpCiE,SAAS,GAAG5B,GAAG;MACjB;MACAA,GAAG,IAAIjC,IAAI,CAACP,MAAM;IACpB;IAEA,OAAOoE,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC9E,SAAS,CAACE,MAAM,GAAGgD,GAAG,GAAGA,GAAG,GAAG,CAAC,GAAG4B,SAAS;EAC7E;EAEAC,cAAcA,CAAA,EAAW;IACvB,IAAIlB,KAAK,GAAG,CAAC;IAEb,MAAMjD,KAAK,GAAG,IAAI,CAACjB,MAAM;IACzB,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACZ,YAAY,EAAEY,CAAC,EAAE,EAAE;MAC1C,IAAII,KAAK,CAACJ,CAAC,CAAC,CAACK,IAAI,OAAuB,EAAE;QACxCgD,KAAK,EAAE;MACT;IACF;IAEA,OAAO,IAAI,CAAC7D,SAAS,CAACC,IAAI,GAAG4D,KAAK;EACpC;AACF;AAACmB,OAAA,CAAAC,OAAA,GAAA/F,MAAA", "ignoreList": []}