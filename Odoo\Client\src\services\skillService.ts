import api from '../lib/api';
import { 
  Skill, 
  CreateSkillRequest, 
  SkillResponse, 
  SkillsResponse 
} from '../types/api';

// Skills API service
export const skillService = {
  // Add offered skill
  addOfferedSkill: async (userId: number, skillData: CreateSkillRequest): Promise<SkillResponse> => {
    try {
      const response = await api.post('/skills/offered', { 
        user_id: userId, 
        ...skillData 
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to add skill');
    }
  },

  // Add wanted skill
  addWantedSkill: async (userId: number, skillData: CreateSkillRequest): Promise<SkillResponse> => {
    try {
      const response = await api.post('/skills/wanted', { 
        user_id: userId, 
        ...skillData 
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to add skill');
    }
  },

  // Get user's offered skills
  getUserOfferedSkills: async (userId: number): Promise<SkillsResponse> => {
    try {
      const response = await api.get(`/skills/offered/user/${userId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch offered skills');
    }
  },

  // Get user's wanted skills
  getUserWantedSkills: async (userId: number): Promise<SkillsResponse> => {
    try {
      const response = await api.get(`/skills/wanted/user/${userId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch wanted skills');
    }
  },

  // Get all offered skills
  getAllOfferedSkills: async (): Promise<SkillsResponse> => {
    try {
      const response = await api.get('/skills/offered');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch skills');
    }
  },

  // Search offered skills
  searchOfferedSkills: async (query: string): Promise<SkillsResponse> => {
    try {
      const response = await api.get(`/skills/search?q=${encodeURIComponent(query)}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to search skills');
    }
  },

  // Get skills by category
  getSkillsByCategory: async (category: string): Promise<SkillsResponse> => {
    try {
      const response = await api.get(`/skills/category/${encodeURIComponent(category)}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to fetch skills by category');
    }
  },

  // Delete offered skill
  deleteOfferedSkill: async (skillId: number, userId: number): Promise<void> => {
    try {
      await api.delete(`/skills/offered/${skillId}`, { 
        data: { user_id: userId } 
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete skill');
    }
  },

  // Delete wanted skill
  deleteWantedSkill: async (skillId: number, userId: number): Promise<void> => {
    try {
      await api.delete(`/skills/wanted/${skillId}`, { 
        data: { user_id: userId } 
      });
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Failed to delete skill');
    }
  },
};
