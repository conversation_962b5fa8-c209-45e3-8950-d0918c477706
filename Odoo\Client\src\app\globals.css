@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
    scroll-behavior: smooth;
  }
  
  body {
    @apply text-gray-900 antialiased;
    background-color: #F4E8DA;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  /* Custom text rendering for Pinterest design aesthetics */
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center font-medium rounded-button transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn px-6 py-3 text-body-medium text-white bg-primary-500 hover:bg-primary-600 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply btn px-6 py-3 text-body-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }

  .btn-ghost {
    @apply btn px-4 py-2 text-body-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-primary-500;
  }

  /* Input Components */
  .input {
    @apply block w-full px-4 py-3 border border-gray-300 rounded-button text-body-medium placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .input-error {
    @apply border-error-500 focus:ring-error-500 focus:border-error-500;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-card border border-gray-200 transition-all duration-300;
  }

  .card-interactive {
    @apply card shadow-sm hover:shadow-md hover:scale-[1.02] cursor-pointer;
  }

  .card-mobile {
    @apply card shadow-lg p-5 overflow-hidden;
  }

  /* Skill Tags */
  .skill-tag {
    @apply inline-flex items-center px-4 py-2 rounded-chip text-caption font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-all duration-200 cursor-pointer border border-gray-200;
  }

  .skill-tag-selected {
    @apply bg-primary-500 text-white hover:bg-primary-600 border-primary-500;
  }

  /* Background Utilities */
  .bg-gradient-warm {
    background: linear-gradient(135deg, #F4E8DA 0%, #ede5d8 100%);
  }

  .bg-gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }

  /* Text Utilities */
  .text-gradient {
    @apply bg-clip-text text-transparent;
  }

  /* Layout Utilities */
  .section-padding {
    @apply py-16 md:py-20 lg:py-24;
  }

  .container-padding {
    @apply px-6 lg:px-8;
  }
}


@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  /* Pinterest-inspired glass effects */
  .glass-effect {
    @apply bg-white/80 backdrop-blur-md border border-white/30 shadow-lg;
  }
  
  .glass-effect-dark {
    @apply bg-gray-900/80 backdrop-blur-md border border-gray-700/30;
  }
  
  /* Gradient utilities matching Pinterest design */
  .gradient-primary {
    background: linear-gradient(135deg, #ff6b5b 0%, #e53e3e 100%);
  }
  
  .gradient-warm {
    background: linear-gradient(135deg, #f5f0e8 0%, #ede5d8 100%);
  }
  
  .gradient-content {
    background: linear-gradient(180deg, rgba(255,107,91,0.1) 0%, rgba(255,107,91,0.05) 100%);
  }
  
  .text-gradient {
    background: linear-gradient(135deg, #ff6b5b 0%, #e53e3e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Shadow utilities for depth */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 20px -5px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  /* Responsive utilities */
  .container-mobile {
    @apply max-w-sm mx-auto px-4;
  }
  
  .container-tablet {
    @apply max-w-2xl mx-auto px-6;
  }
  
  .container-desktop {
    @apply max-w-7xl mx-auto px-8;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Content layout utilities */
  .masonry-grid {
    column-count: 1;
    column-gap: 1rem;
  }
  
  @media (min-width: 640px) {
    .masonry-grid {
      column-count: 2;
    }
  }
  
  @media (min-width: 1024px) {
    .masonry-grid {
      column-count: 3;
    }
  }
  
  .masonry-item {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
}
