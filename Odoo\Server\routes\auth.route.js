import express from 'express';
import { userOperations } from '../SupabaseClient/operations.js';

const router = express.Router();

// Email validation function
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

// Register new user
router.post('/register', async (req, res) => {
    try {
        const { username, email, password, location } = req.body;

        // Validate required fields
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Username, email, and password are required.'
            });
        }

        // Validate email format
        if (!isValidEmail(email)) {
            return res.status(400).json({
                success: false,
                message: 'Please enter a valid email address.'
            });
        }

        // Validate password length
        if (password.length < 6) {
            return res.status(400).json({
                success: false,
                message: 'Password must be at least 6 characters long.'
            });
        }

        // Check if user already exists
        try {
            const existingUser = await userOperations.getUserByEmail(email);
            if (existingUser) {
                return res.status(409).json({
                    success: false,
                    message: 'User already exists with this email.'
                });
            }
        } catch (checkError) {
            // If error is not "no rows found", it's a real error
            if (checkError.code !== 'PGRST116') {
                console.error('Error checking existing user:', checkError);
                return res.status(500).json({
                    success: false,
                    message: 'Error checking user existence.'
                });
            }
            // If PGRST116, user doesn't exist, which is what we want
        }

        // Create user in database
        const newUser = await userOperations.createUser({
            username,
            email,
            password, // Note: In production, hash this password
            location: location || '',
            availability: 'available'
        });

        res.status(201).json({
            success: true,
            message: 'User created successfully.',
            user: {
                id: newUser.id,
                username: newUser.username,
                email: newUser.email,
                location: newUser.location,
                availability: newUser.availability
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Registration failed'
        });
    }
});

// Login user
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Email and password are required.'
            });
        }

        // Validate email format
        if (!isValidEmail(email)) {
            return res.status(400).json({
                success: false,
                message: 'Please enter a valid email address.'
            });
        }

        // Authenticate user with simple password check
        let user;
        try {
            user = await userOperations.authenticateUser(email, password);
        } catch (authError) {
            console.error('Authentication error:', authError);
            return res.status(500).json({
                success: false,
                message: 'Authentication service error.'
            });
        }

        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid email or password.'
            });
        }

        res.json({
            success: true,
            message: 'Login successful',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                location: user.location,
                avatar: user.avatar,
                availability: user.availability
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(401).json({
            success: false,
            message: 'Invalid credentials'
        });
    }
});

// Get current user by ID
router.get('/me/:userId', async (req, res) => {
    try {
        const userId = req.params.userId;

        const userProfile = await userOperations.getUserById(userId);

        if (!userProfile) {
            return res.status(404).json({
                success: false,
                message: 'User profile not found'
            });
        }

        res.json({
            success: true,
            user: {
                id: userProfile.id,
                username: userProfile.username,
                email: userProfile.email,
                location: userProfile.location,
                avatar: userProfile.avatar,
                availability: userProfile.availability
            }
        });

    } catch (error) {
        console.error('Get user error:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching user profile'
        });
    }
});

// Simple logout (client-side session clearing)
router.post('/logout', async (req, res) => {
    res.json({
        success: true,
        message: 'Logged out successfully'
    });
});

export default router;