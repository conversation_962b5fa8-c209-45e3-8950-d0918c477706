import express from 'express';
import { authOperations, userOperations } from '../DataBase_Client/skillSwapDB.js';

const router = express.Router();

// Register new user - Pure Supabase Auth
router.post('/register', async (req, res) => {
    try {
        const { name, email, password, location } = req.body;

        if (!name || !email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Name, email, and password are required.'
            });
        }

        // Register user with Supabase Auth (this will automatically create the user profile via trigger)
        const { auth } = await authOperations.registerUser(email, password, {
            name,
            location: location || ''
        });

        res.status(201).json({
            success: true,
            message: 'User created successfully. Please check your email to verify your account.',
            user: {
                id: auth.user.id,
                email: auth.user.email,
                name: name,
                location: location || ''
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Registration failed'
        });
    }
});

// Login user - Pure Supabase Auth
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Email and password are required.'
            });
        }

        // Sign in with Supabase
        const authData = await authOperations.signInUser(email, password);

        // Get user profile
        const userProfile = await userOperations.getUserById(authData.user.id);

        if (!userProfile) {
            return res.status(404).json({
                success: false,
                message: 'User profile not found.'
            });
        }

        res.json({
            success: true,
            message: 'Login successful',
            user: {
                id: userProfile.id,
                name: userProfile.name,
                email: userProfile.email,
                location: userProfile.location,
                avatar: userProfile.avatar,
                privacy: userProfile.privacy,
                availability: userProfile.availability
            },
            session: {
                access_token: authData.session.access_token,
                refresh_token: authData.session.refresh_token,
                expires_at: authData.session.expires_at,
                user: authData.user
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(401).json({
            success: false,
            message: 'Invalid credentials'
        });
    }
});

// Get current user - Using Supabase token
router.get('/me', async (req, res) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'No token provided'
            });
        }

        const token = authHeader.substring(7);

        // Verify token with Supabase and get user
        const user = await authOperations.getUser(token);
        const userProfile = await userOperations.getUserById(user.id);

        if (!userProfile) {
            return res.status(404).json({
                success: false,
                message: 'User profile not found'
            });
        }

        res.json({
            success: true,
            user: {
                id: userProfile.id,
                name: userProfile.name,
                email: userProfile.email,
                location: userProfile.location,
                avatar: userProfile.avatar,
                privacy: userProfile.privacy,
                availability: userProfile.availability
            }
        });

    } catch (error) {
        console.error('Get user error:', error);
        res.status(401).json({
            success: false,
            message: 'Invalid or expired token'
        });
    }
});

// Logout user
router.post('/logout', async (req, res) => {
    try {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            await authOperations.signOut(token);
        }

        res.json({
            success: true,
            message: 'Logged out successfully'
        });

    } catch (error) {
        console.error('Logout error:', error);
        res.json({
            success: true,
            message: 'Logged out successfully'
        });
    }
});

export default router;