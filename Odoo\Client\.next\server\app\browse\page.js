/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/browse/page";
exports.ids = ["app/browse/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fbrowse%2Fpage&page=%2Fbrowse%2Fpage&appPaths=%2Fbrowse%2Fpage&pagePath=private-next-app-dir%2Fbrowse%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fbrowse%2Fpage&page=%2Fbrowse%2Fpage&appPaths=%2Fbrowse%2Fpage&pagePath=private-next-app-dir%2Fbrowse%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'browse',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/browse/page.tsx */ \"(rsc)/./src/app/browse/page.tsx\")), \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/browse/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/browse/page\",\n        pathname: \"/browse\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fbrowse%2Fpage&page=%2Fbrowse%2Fpage&appPaths=%2Fbrowse%2Fpage&pagePath=private-next-app-dir%2Fbrowse%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Ccomponents%5Cui%5Cnavigation.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Ccomponents%5Cui%5Cnavigation.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/navigation.tsx */ \"(ssr)/./src/components/ui/navigation.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q0hhY2thdGhvbiU1Q09kb28lNUNPZG9vJTVDQ2xpZW50JTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q0hhY2thdGhvbiU1Q09kb28lNUNPZG9vJTVDQ2xpZW50JTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1DJTNBJTVDSGFja2F0aG9uJTVDT2RvbyU1Q09kb28lNUNDbGllbnQlNUNzcmMlNUNjb21wb25lbnRzJTVDdWklNUNuYXZpZ2F0aW9uLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9za2lsbC1zd2FwLWNsaWVudC8/YzliOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXEhhY2thdGhvblxcXFxPZG9vXFxcXE9kb29cXFxcQ2xpZW50XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHVpXFxcXG5hdmlnYXRpb24udHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Ccomponents%5Cui%5Cnavigation.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cbrowse%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cbrowse%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/browse/page.tsx */ \"(ssr)/./src/app/browse/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q0hhY2thdGhvbiU1Q09kb28lNUNPZG9vJTVDQ2xpZW50JTVDc3JjJTVDYXBwJTVDYnJvd3NlJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2tpbGwtc3dhcC1jbGllbnQvP2NmYzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxIYWNrYXRob25cXFxcT2Rvb1xcXFxPZG9vXFxcXENsaWVudFxcXFxzcmNcXFxcYXBwXFxcXGJyb3dzZVxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cbrowse%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/browse/page.tsx":
/*!*********************************!*\
  !*** ./src/app/browse/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BrowseSkillsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,FunnelIcon,HeartIcon,MagnifyingGlassIcon,MapPinIcon,StarIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,FunnelIcon,HeartIcon,MagnifyingGlassIcon,MapPinIcon,StarIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,FunnelIcon,HeartIcon,MagnifyingGlassIcon,MapPinIcon,StarIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,FunnelIcon,HeartIcon,MagnifyingGlassIcon,MapPinIcon,StarIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,FunnelIcon,HeartIcon,MagnifyingGlassIcon,MapPinIcon,StarIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,FunnelIcon,HeartIcon,MagnifyingGlassIcon,MapPinIcon,StarIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,FunnelIcon,HeartIcon,MagnifyingGlassIcon,MapPinIcon,StarIcon,UserIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst categories = [\n    \"All Categories\",\n    \"Programming\",\n    \"Design\",\n    \"Marketing\",\n    \"Business\",\n    \"Languages\",\n    \"Music\",\n    \"Art\",\n    \"Cooking\",\n    \"Fitness\"\n];\nfunction BrowseSkillsPage() {\n    const [skills, setSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredSkills, setFilteredSkills] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All Categories\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadSkills = async ()=>{\n            try {\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.dbHelpers.skills.getAllOffered();\n                if (error) {\n                    console.error(\"Error fetching skills:\", error);\n                    return;\n                }\n                setSkills(data || []);\n                setFilteredSkills(data || []);\n            } catch (error) {\n                console.error(\"Error loading skills:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        loadSkills();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let filtered = skills;\n        // Filter by search query\n        if (searchQuery) {\n            filtered = filtered.filter((skill)=>skill.skill_name.toLowerCase().includes(searchQuery.toLowerCase()) || skill.description.toLowerCase().includes(searchQuery.toLowerCase()) || (skill.users?.name || \"\").toLowerCase().includes(searchQuery.toLowerCase()));\n        }\n        // Filter by category\n        if (selectedCategory !== \"All Categories\") {\n            filtered = filtered.filter((skill)=>skill.category === selectedCategory);\n        }\n        setFilteredSkills(filtered);\n    }, [\n        searchQuery,\n        selectedCategory,\n        skills\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Browse Skills\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Discover amazing skills from talented people in your community. Find someone to learn from or teach what you know.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row gap-4 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1 max-w-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search skills, people, or keywords...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"input pl-10 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedCategory,\n                                            onChange: (e)=>setSelectedCategory(e.target.value),\n                                            className: \"input min-w-[200px]\",\n                                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-secondary flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Filters\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                filteredSkills.length,\n                                \" skill\",\n                                filteredSkills.length !== 1 ? \"s\" : \"\",\n                                \" found\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: filteredSkills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card p-6 hover:shadow-lg transition-all duration-300 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-10 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center\",\n                                                children: skill.users?.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: skill.users.avatar,\n                                                    alt: skill.users.name,\n                                                    className: \"h-10 w-10 rounded-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3 flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: skill.users?.name || \"Anonymous\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            skill.users?.location || \"Location not specified\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 text-yellow-400 fill-current\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 ml-1\",\n                                                        children: \"5.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                children: skill.skill_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 text-sm leading-relaxed\",\n                                                children: skill.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block px-3 py-1 bg-primary-100 text-primary-700 text-xs rounded-full font-medium\",\n                                                children: skill.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-red-500 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"btn-primary text-sm flex items-center\",\n                                                        children: [\n                                                            \"Connect\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, skill.id, true, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    filteredSkills.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_FunnelIcon_HeartIcon_MagnifyingGlassIcon_MapPinIcon_StarIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-16 w-16 text-gray-300 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No skills found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Try adjusting your search terms or filters to find what you're looking for.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\browse\\\\page.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/browse/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/navigation.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer),\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ Navigation,Footer auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Browse Skills\",\n        href: \"/browse\",\n        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"My Swaps\",\n        href: \"/swaps\",\n        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Profile\",\n        href: \"/profile\",\n        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    }\n];\nfunction Navigation() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-primary-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"SkillSwap\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${isActive ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-px bg-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/profile\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                            className: \"md:hidden p-2 text-gray-400 hover:text-gray-600\",\n                            children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t border-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    className: `flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${isActive ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 pt-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/profile\",\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    className: \"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Account Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-gray-200 mt-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: \"SkillSwap\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 max-w-md\",\n                                    children: \"Connect with people to exchange skills and knowledge. Learn something new while teaching what you know.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 mb-4\",\n                                    children: \"Platform\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/browse\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Browse Skills\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/dashboard\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/swaps\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"My Swaps\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/profile\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 mb-4\",\n                                    children: \"Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/help\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Help Center\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/privacy\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/terms\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200 pt-8 mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-gray-600\",\n                        children: \"\\xa9 2024 SkillSwap. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authHelpers: () => (/* binding */ authHelpers),\n/* harmony export */   dbHelpers: () => (/* binding */ dbHelpers),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://faicfimolnietqtlnbss.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZhaWNmaW1vbG5pZXRxdGxuYnNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzMTM5MTUsImV4cCI6MjA2Nzg4OTkxNX0.Ub7d2T6PZu8cJY0uusiMFBsGWbaKio2pJuxZxhOyf34\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Auth helpers\nconst authHelpers = {\n    // Sign up with email and password\n    async signUp (email, password, userData) {\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: userData\n            }\n        });\n        return {\n            data,\n            error\n        };\n    },\n    // Sign in with email and password\n    async signIn (email, password) {\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            data,\n            error\n        };\n    },\n    // Sign out\n    async signOut () {\n        const { error } = await supabase.auth.signOut();\n        return {\n            error\n        };\n    },\n    // Get current session\n    async getSession () {\n        const { data: { session }, error } = await supabase.auth.getSession();\n        return {\n            session,\n            error\n        };\n    },\n    // Get current user\n    async getUser () {\n        const { data: { user }, error } = await supabase.auth.getUser();\n        return {\n            user,\n            error\n        };\n    }\n};\n// Database helpers\nconst dbHelpers = {\n    // User operations\n    users: {\n        async getById (id) {\n            const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", id).single();\n            return {\n                data,\n                error\n            };\n        },\n        async getByEmail (email) {\n            const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"email\", email).single();\n            return {\n                data,\n                error\n            };\n        },\n        async create (userData) {\n            const { data, error } = await supabase.from(\"users\").insert([\n                userData\n            ]).select().single();\n            return {\n                data,\n                error\n            };\n        },\n        async update (id, updates) {\n            const { data, error } = await supabase.from(\"users\").update(updates).eq(\"id\", id).select().single();\n            return {\n                data,\n                error\n            };\n        },\n        async getAll () {\n            const { data, error } = await supabase.from(\"users\").select(\"id, email, name, location, avatar, availability, created_at\").eq(\"privacy\", \"public\");\n            return {\n                data,\n                error\n            };\n        }\n    },\n    // Skills operations\n    skills: {\n        async getOfferedByUser (userId) {\n            const { data, error } = await supabase.from(\"skill_offered\").select(\"*\").eq(\"user_id\", userId);\n            return {\n                data,\n                error\n            };\n        },\n        async getWantedByUser (userId) {\n            const { data, error } = await supabase.from(\"skills_wanted\").select(\"*\").eq(\"user_id\", userId);\n            return {\n                data,\n                error\n            };\n        },\n        async getAllOffered () {\n            const { data, error } = await supabase.from(\"skill_offered\").select(`\n          *,\n          users!inner (\n            name,\n            location,\n            avatar,\n            privacy\n          )\n        `).eq(\"users.privacy\", \"public\").order(\"created_at\", {\n                ascending: false\n            });\n            return {\n                data,\n                error\n            };\n        },\n        async searchOffered (searchTerm) {\n            const { data, error } = await supabase.from(\"skill_offered\").select(`\n          *,\n          users!inner (\n            name,\n            location,\n            avatar,\n            privacy\n          )\n        `).eq(\"users.privacy\", \"public\").or(`skill_name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,category.ilike.%${searchTerm}%`);\n            return {\n                data,\n                error\n            };\n        },\n        async getByCategory (category) {\n            const { data, error } = await supabase.from(\"skill_offered\").select(`\n          *,\n          users!inner (\n            name,\n            location,\n            avatar,\n            privacy\n          )\n        `).eq(\"users.privacy\", \"public\").eq(\"category\", category);\n            return {\n                data,\n                error\n            };\n        },\n        async addOffered (skillData) {\n            const { data, error } = await supabase.from(\"skill_offered\").insert([\n                skillData\n            ]).select().single();\n            return {\n                data,\n                error\n            };\n        },\n        async addWanted (skillData) {\n            const { data, error } = await supabase.from(\"skills_wanted\").insert([\n                skillData\n            ]).select().single();\n            return {\n                data,\n                error\n            };\n        },\n        async deleteOffered (skillId, userId) {\n            const { error } = await supabase.from(\"skill_offered\").delete().eq(\"id\", skillId).eq(\"user_id\", userId);\n            return {\n                error\n            };\n        },\n        async deleteWanted (skillId, userId) {\n            const { error } = await supabase.from(\"skills_wanted\").delete().eq(\"id\", skillId).eq(\"user_id\", userId);\n            return {\n                error\n            };\n        }\n    },\n    // Swap operations\n    swaps: {\n        async create (swapData) {\n            const { data, error } = await supabase.from(\"swap_requests\").insert([\n                swapData\n            ]).select().single();\n            return {\n                data,\n                error\n            };\n        },\n        async getByUser (userId) {\n            const { data, error } = await supabase.from(\"swap_requests\").select(`\n          *,\n          requester:users!swap_requests_requester_id_fkey (\n            id, name, avatar, location\n          ),\n          receiver:users!swap_requests_receiver_id_fkey (\n            id, name, avatar, location\n          ),\n          offered_skill:skill_offered (\n            id, skill_name, category\n          ),\n          wanted_skill:skills_wanted (\n            id, skill_name, category\n          )\n        `).or(`requester_id.eq.${userId},receiver_id.eq.${userId}`).order(\"created_at\", {\n                ascending: false\n            });\n            return {\n                data,\n                error\n            };\n        },\n        async updateStatus (swapId, status, userId) {\n            const { data, error } = await supabase.from(\"swap_requests\").update({\n                status,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", swapId).eq(\"receiver_id\", userId) // Only receiver can update status\n            .select().single();\n            return {\n                data,\n                error\n            };\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"86825a0c7a02\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2tpbGwtc3dhcC1jbGllbnQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzczYjgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4NjgyNWEwYzdhMDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/browse/page.tsx":
/*!*********************************!*\
  !*** ./src/app/browse/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Hackathon\Odoo\Odoo\Client\src\app\browse\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ui_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/navigation */ \"(rsc)/./src/components/ui/navigation.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"SkillSwap - Exchange Skills, Build Connections\",\n    description: \"Connect with people to exchange skills and knowledge. Learn something new while teaching what you know.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation__WEBPACK_IMPORTED_MODULE_2__.Navigation, {}, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation__WEBPACK_IMPORTED_MODULE_2__.Footer, {}, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQzBDO0FBSXpELE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdYLCtKQUFlO3NCQUM5Qiw0RUFBQ1k7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDVixpRUFBVUE7Ozs7O2tDQUNYLDhEQUFDWTt3QkFBS0YsV0FBVTtrQ0FDYko7Ozs7OztrQ0FFSCw4REFBQ0wsNkRBQU1BOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9za2lsbC1zd2FwLWNsaWVudC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgTmF2aWdhdGlvbiwgRm9vdGVyIH0gZnJvbSAnLi4vY29tcG9uZW50cy91aS9uYXZpZ2F0aW9uJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnU2tpbGxTd2FwIC0gRXhjaGFuZ2UgU2tpbGxzLCBCdWlsZCBDb25uZWN0aW9ucycsXG4gIGRlc2NyaXB0aW9uOiAnQ29ubmVjdCB3aXRoIHBlb3BsZSB0byBleGNoYW5nZSBza2lsbHMgYW5kIGtub3dsZWRnZS4gTGVhcm4gc29tZXRoaW5nIG5ldyB3aGlsZSB0ZWFjaGluZyB3aGF0IHlvdSBrbm93LicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgPE5hdmlnYXRpb24gLz5cbiAgICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L21haW4+XG4gICAgICAgICAgPEZvb3RlciAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIk5hdmlnYXRpb24iLCJGb290ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJkaXYiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/navigation.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ e1),
/* harmony export */   Navigation: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Hackathon\Odoo\Odoo\Client\src\components\ui\navigation.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Hackathon\Odoo\Odoo\Client\src\components\ui\navigation.tsx#Navigation`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Hackathon\Odoo\Odoo\Client\src\components\ui\navigation.tsx#Footer`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@heroicons","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fbrowse%2Fpage&page=%2Fbrowse%2Fpage&appPaths=%2Fbrowse%2Fpage&pagePath=private-next-app-dir%2Fbrowse%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();