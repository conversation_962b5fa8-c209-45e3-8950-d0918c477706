import dotenv from 'dotenv';
import pkg from 'pg';

dotenv.config();

const { Pool } = pkg;

const pool = new Pool({
    host: process.env.POSTGRES_HOST,
    database: process.env.POSTGRES_DB,
    port: process.env.POSTGRES_PORT,
    user: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
});

console.log('Testing database connection...');
console.log('Host:', process.env.POSTGRES_HOST);
console.log('Database:', process.env.POSTGRES_DB);
console.log('Port:', process.env.POSTGRES_PORT);
console.log('User:', process.env.POSTGRES_USER);
console.log('Password:', process.env.POSTGRES_PASSWORD ? '***' : 'NOT SET');

pool.connect((err, client, release) => {
    if (err) {
        console.error('Database connection error:', err.stack);
    } else {
        console.log('✅ Connected to PostgreSQL database successfully!');
        release();
    }
    pool.end();
});
