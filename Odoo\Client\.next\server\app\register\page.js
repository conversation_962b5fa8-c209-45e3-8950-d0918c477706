/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/register/page";
exports.ids = ["app/register/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'register',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(rsc)/./src/app/register/page.tsx\")), \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/register/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/register/page\",\n        pathname: \"/register\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Ccomponents%5Cui%5Cnavigation.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Ccomponents%5Cui%5Cnavigation.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/navigation.tsx */ \"(ssr)/./src/components/ui/navigation.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q0hhY2thdGhvbiU1Q09kb28lNUNPZG9vJTVDQ2xpZW50JTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q0hhY2thdGhvbiU1Q09kb28lNUNPZG9vJTVDQ2xpZW50JTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1DJTNBJTVDSGFja2F0aG9uJTVDT2RvbyU1Q09kb28lNUNDbGllbnQlNUNzcmMlNUNjb21wb25lbnRzJTVDdWklNUNuYXZpZ2F0aW9uLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9za2lsbC1zd2FwLWNsaWVudC8/YzliOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXEhhY2thdGhvblxcXFxPZG9vXFxcXE9kb29cXFxcQ2xpZW50XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHVpXFxcXG5hdmlnYXRpb24udHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Ccomponents%5Cui%5Cnavigation.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cregister%5Cpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cregister%5Cpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(ssr)/./src/app/register/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q0hhY2thdGhvbiU1Q09kb28lNUNPZG9vJTVDQ2xpZW50JTVDc3JjJTVDYXBwJTVDcmVnaXN0ZXIlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9za2lsbC1zd2FwLWNsaWVudC8/YzMyZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXEhhY2thdGhvblxcXFxPZG9vXFxcXE9kb29cXFxcQ2xpZW50XFxcXHNyY1xcXFxhcHBcXFxccmVnaXN0ZXJcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp%5Cregister%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction RegisterPage() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        location: \"\"\n    });\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setLoading(true);\n        if (formData.password !== formData.confirmPassword) {\n            setError(\"Passwords do not match\");\n            setLoading(false);\n            return;\n        }\n        try {\n            const response = await fetch(`${\"http://localhost:5000\"}/api/register`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    email: formData.email,\n                    password: formData.password,\n                    location: formData.location\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                localStorage.setItem(\"token\", data.token);\n                localStorage.setItem(\"user\", JSON.stringify(data.user));\n                // Redirect to dashboard\n                window.location.href = \"/dashboard\";\n            } else {\n                const errorData = await response.json();\n                setError(errorData.message || \"Registration failed\");\n            }\n        } catch (error) {\n            setError(\"Network error. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: \"Create your account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: [\n                                \"Or\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n                                    children: \"sign in to your existing account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"name\",\n                                            className: \"sr-only\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"name\",\n                                            name: \"name\",\n                                            type: \"text\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Full Name\",\n                                            value: formData.name,\n                                            onChange: handleChange\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"sr-only\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"email\",\n                                            name: \"email\",\n                                            type: \"email\",\n                                            autoComplete: \"email\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Email address\",\n                                            value: formData.email,\n                                            onChange: handleChange\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"location\",\n                                            className: \"sr-only\",\n                                            children: \"Location\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"location\",\n                                            name: \"location\",\n                                            type: \"text\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Location (e.g., New York, NY)\",\n                                            value: formData.location,\n                                            onChange: handleChange\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"sr-only\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"password\",\n                                            name: \"password\",\n                                            type: \"password\",\n                                            autoComplete: \"new-password\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Password\",\n                                            value: formData.password,\n                                            onChange: handleChange\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"confirmPassword\",\n                                            className: \"sr-only\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: \"password\",\n                                            autoComplete: \"new-password\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\",\n                                            placeholder: \"Confirm Password\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleChange\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-600 text-sm text-center\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\",\n                                children: loading ? \"Creating account...\" : \"Create account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\register\\\\page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/register/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/navigation.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer),\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,BellIcon,HomeIcon,MagnifyingGlassIcon,SparklesIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ Navigation,Footer auto */ \n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        name: \"Browse Skills\",\n        href: \"/browse\",\n        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        name: \"My Swaps\",\n        href: \"/swaps\",\n        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        name: \"Profile\",\n        href: \"/profile\",\n        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    }\n];\nfunction Navigation() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-8 w-8 text-primary-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"SkillSwap\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${isActive ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-6 w-px bg-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/profile\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 w-8 rounded-full bg-gradient-to-r from-primary-500 to-secondary-500 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                            className: \"md:hidden p-2 text-gray-400 hover:text-gray-600\",\n                            children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t border-gray-200 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            navigation.map((item)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    className: `flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${isActive ? \"bg-primary-100 text-primary-700\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 19\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 pt-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/profile\",\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    className: \"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Account Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-gray-200 mt-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_BellIcon_HomeIcon_MagnifyingGlassIcon_SparklesIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-primary-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900\",\n                                            children: \"SkillSwap\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 max-w-md\",\n                                    children: \"Connect with people to exchange skills and knowledge. Learn something new while teaching what you know.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 mb-4\",\n                                    children: \"Platform\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/browse\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Browse Skills\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/dashboard\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/swaps\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"My Swaps\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/profile\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 mb-4\",\n                                    children: \"Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/help\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Help Center\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/privacy\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/terms\",\n                                                className: \"hover:text-gray-900\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-200 pt-8 mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-sm text-gray-600\",\n                        children: \"\\xa9 2024 SkillSwap. All rights reserved.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\navigation.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/navigation.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"86825a0c7a02\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2tpbGwtc3dhcC1jbGllbnQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzczYjgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4NjgyNWEwYzdhMDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ui_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/navigation */ \"(rsc)/./src/components/ui/navigation.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"SkillSwap - Exchange Skills, Build Connections\",\n    description: \"Connect with people to exchange skills and knowledge. Learn something new while teaching what you know.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation__WEBPACK_IMPORTED_MODULE_2__.Navigation, {}, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_navigation__WEBPACK_IMPORTED_MODULE_2__.Footer, {}, void 0, false, {\n                        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQzBDO0FBSXpELE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdYLCtKQUFlO3NCQUM5Qiw0RUFBQ1k7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDVixpRUFBVUE7Ozs7O2tDQUNYLDhEQUFDWTt3QkFBS0YsV0FBVTtrQ0FDYko7Ozs7OztrQ0FFSCw4REFBQ0wsNkRBQU1BOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9za2lsbC1zd2FwLWNsaWVudC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgTmF2aWdhdGlvbiwgRm9vdGVyIH0gZnJvbSAnLi4vY29tcG9uZW50cy91aS9uYXZpZ2F0aW9uJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnU2tpbGxTd2FwIC0gRXhjaGFuZ2UgU2tpbGxzLCBCdWlsZCBDb25uZWN0aW9ucycsXG4gIGRlc2NyaXB0aW9uOiAnQ29ubmVjdCB3aXRoIHBlb3BsZSB0byBleGNoYW5nZSBza2lsbHMgYW5kIGtub3dsZWRnZS4gTGVhcm4gc29tZXRoaW5nIG5ldyB3aGlsZSB0ZWFjaGluZyB3aGF0IHlvdSBrbm93LicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgPE5hdmlnYXRpb24gLz5cbiAgICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L21haW4+XG4gICAgICAgICAgPEZvb3RlciAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIk5hdmlnYXRpb24iLCJGb290ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJkaXYiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Hackathon\Odoo\Odoo\Client\src\app\register\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/ui/navigation.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ e1),
/* harmony export */   Navigation: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Hackathon\Odoo\Odoo\Client\src\components\ui\navigation.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Hackathon\Odoo\Odoo\Client\src\components\ui\navigation.tsx#Navigation`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Hackathon\Odoo\Odoo\Client\src\components\ui\navigation.tsx#Footer`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CHackathon%5COdoo%5COdoo%5CClient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();