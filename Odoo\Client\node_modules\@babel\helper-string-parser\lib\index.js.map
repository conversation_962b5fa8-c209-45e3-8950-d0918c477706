{"version": 3, "names": ["isDigit", "code", "forbiddenNumericSeparatorSiblings", "decBinOct", "Set", "hex", "isAllowedNumericSeparatorSibling", "bin", "ch", "oct", "dec", "readStringContents", "type", "input", "pos", "lineStart", "curLine", "errors", "initialPos", "initialLineStart", "initialCurLine", "out", "firstInvalidLoc", "chunkStart", "length", "unterminated", "slice", "charCodeAt", "isStringEnd", "res", "readEscapedChar", "str", "containsInvalid", "inTemplate", "throwOnInvalid", "readHexChar", "String", "fromCharCode", "readCodePoint", "fromCodePoint", "strictNumericEscape", "startPos", "match", "exec", "octalStr", "octal", "parseInt", "next", "len", "forceLen", "n", "readInt", "invalidEscapeSequence", "radix", "allowNumSeparator", "bailOnError", "start", "forbiddenSiblings", "isAllowedSibling", "invalid", "total", "i", "e", "Infinity", "val", "prev", "numericSeparatorInEscapeSequence", "Number", "isNaN", "has", "unexpectedNumericSeparator", "_isDigit", "invalidDigit", "indexOf", "invalidCodePoint"], "sources": ["../src/index.ts"], "sourcesContent": ["// We inline this package\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport * as charCodes from \"charcodes\";\n\n// The following character codes are forbidden from being\n// an immediate sibling of NumericLiteralSeparator _\nconst forbiddenNumericSeparatorSiblings = {\n  decBinOct: new Set<number>([\n    charCodes.dot,\n    charCodes.uppercaseB,\n    charCodes.uppercaseE,\n    charCodes.uppercaseO,\n    charCodes.underscore, // multiple separators are not allowed\n    charCodes.lowercaseB,\n    charCodes.lowercaseE,\n    charCodes.lowercaseO,\n  ]),\n  hex: new Set<number>([\n    charCodes.dot,\n    charCodes.uppercaseX,\n    charCodes.underscore, // multiple separators are not allowed\n    charCodes.lowercaseX,\n  ]),\n};\n\nconst isAllowedNumericSeparatorSibling = {\n  // 0 - 1\n  bin: (ch: number) => ch === charCodes.digit0 || ch === charCodes.digit1,\n\n  // 0 - 7\n  oct: (ch: number) => ch >= charCodes.digit0 && ch <= charCodes.digit7,\n\n  // 0 - 9\n  dec: (ch: number) => ch >= charCodes.digit0 && ch <= charCodes.digit9,\n\n  // 0 - 9, A - F, a - f,\n  hex: (ch: number) =>\n    (ch >= charCodes.digit0 && ch <= charCodes.digit9) ||\n    (ch >= charCodes.uppercaseA && ch <= charCodes.uppercaseF) ||\n    (ch >= charCodes.lowercaseA && ch <= charCodes.lowercaseF),\n};\n\nexport type StringContentsErrorHandlers = EscapedCharErrorHandlers & {\n  unterminated(\n    initialPos: number,\n    initialLineStart: number,\n    initialCurLine: number,\n  ): void;\n};\n\nexport function readStringContents(\n  type: \"single\" | \"double\" | \"template\",\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  errors: StringContentsErrorHandlers,\n) {\n  const initialPos = pos;\n  const initialLineStart = lineStart;\n  const initialCurLine = curLine;\n\n  let out = \"\";\n  let firstInvalidLoc = null;\n  let chunkStart = pos;\n  const { length } = input;\n  for (;;) {\n    if (pos >= length) {\n      errors.unterminated(initialPos, initialLineStart, initialCurLine);\n      out += input.slice(chunkStart, pos);\n      break;\n    }\n    const ch = input.charCodeAt(pos);\n    if (isStringEnd(type, ch, input, pos)) {\n      out += input.slice(chunkStart, pos);\n      break;\n    }\n    if (ch === charCodes.backslash) {\n      out += input.slice(chunkStart, pos);\n      const res = readEscapedChar(\n        input,\n        pos,\n        lineStart,\n        curLine,\n        type === \"template\",\n        errors,\n      );\n      if (res.ch === null && !firstInvalidLoc) {\n        firstInvalidLoc = { pos, lineStart, curLine };\n      } else {\n        out += res.ch;\n      }\n      ({ pos, lineStart, curLine } = res);\n      chunkStart = pos;\n    } else if (\n      ch === charCodes.lineSeparator ||\n      ch === charCodes.paragraphSeparator\n    ) {\n      ++pos;\n      ++curLine;\n      lineStart = pos;\n    } else if (ch === charCodes.lineFeed || ch === charCodes.carriageReturn) {\n      if (type === \"template\") {\n        out += input.slice(chunkStart, pos) + \"\\n\";\n        ++pos;\n        if (\n          ch === charCodes.carriageReturn &&\n          input.charCodeAt(pos) === charCodes.lineFeed\n        ) {\n          ++pos;\n        }\n        ++curLine;\n        chunkStart = lineStart = pos;\n      } else {\n        errors.unterminated(initialPos, initialLineStart, initialCurLine);\n      }\n    } else {\n      ++pos;\n    }\n  }\n  return process.env.BABEL_8_BREAKING\n    ? { pos, str: out, firstInvalidLoc, lineStart, curLine }\n    : {\n        pos,\n        str: out,\n        firstInvalidLoc,\n        lineStart,\n        curLine,\n        containsInvalid: !!firstInvalidLoc,\n      };\n}\n\nfunction isStringEnd(\n  type: \"single\" | \"double\" | \"template\",\n  ch: number,\n  input: string,\n  pos: number,\n) {\n  if (type === \"template\") {\n    return (\n      ch === charCodes.graveAccent ||\n      (ch === charCodes.dollarSign &&\n        input.charCodeAt(pos + 1) === charCodes.leftCurlyBrace)\n    );\n  }\n  return (\n    ch === (type === \"double\" ? charCodes.quotationMark : charCodes.apostrophe)\n  );\n}\n\ntype EscapedCharErrorHandlers = HexCharErrorHandlers &\n  CodePointErrorHandlers & {\n    strictNumericEscape(pos: number, lineStart: number, curLine: number): void;\n  };\n\nfunction readEscapedChar(\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  inTemplate: boolean,\n  errors: EscapedCharErrorHandlers,\n) {\n  const throwOnInvalid = !inTemplate;\n  pos++; // skip '\\'\n\n  const res = (ch: string | null) => ({ pos, ch, lineStart, curLine });\n\n  const ch = input.charCodeAt(pos++);\n  switch (ch) {\n    case charCodes.lowercaseN:\n      return res(\"\\n\");\n    case charCodes.lowercaseR:\n      return res(\"\\r\");\n    case charCodes.lowercaseX: {\n      let code;\n      ({ code, pos } = readHexChar(\n        input,\n        pos,\n        lineStart,\n        curLine,\n        2,\n        false,\n        throwOnInvalid,\n        errors,\n      ));\n      return res(code === null ? null : String.fromCharCode(code));\n    }\n    case charCodes.lowercaseU: {\n      let code;\n      ({ code, pos } = readCodePoint(\n        input,\n        pos,\n        lineStart,\n        curLine,\n        throwOnInvalid,\n        errors,\n      ));\n      return res(code === null ? null : String.fromCodePoint(code));\n    }\n    case charCodes.lowercaseT:\n      return res(\"\\t\");\n    case charCodes.lowercaseB:\n      return res(\"\\b\");\n    case charCodes.lowercaseV:\n      return res(\"\\u000b\");\n    case charCodes.lowercaseF:\n      return res(\"\\f\");\n    case charCodes.carriageReturn:\n      if (input.charCodeAt(pos) === charCodes.lineFeed) {\n        ++pos;\n      }\n    // fall through\n    case charCodes.lineFeed:\n      lineStart = pos;\n      ++curLine;\n    // fall through\n    case charCodes.lineSeparator:\n    case charCodes.paragraphSeparator:\n      return res(\"\");\n    case charCodes.digit8:\n    case charCodes.digit9:\n      if (inTemplate) {\n        return res(null);\n      } else {\n        errors.strictNumericEscape(pos - 1, lineStart, curLine);\n      }\n    // fall through\n    default:\n      if (ch >= charCodes.digit0 && ch <= charCodes.digit7) {\n        const startPos = pos - 1;\n        const match = /^[0-7]+/.exec(input.slice(startPos, pos + 2));\n\n        let octalStr = match[0];\n\n        let octal = parseInt(octalStr, 8);\n        if (octal > 255) {\n          octalStr = octalStr.slice(0, -1);\n          octal = parseInt(octalStr, 8);\n        }\n        pos += octalStr.length - 1;\n        const next = input.charCodeAt(pos);\n        if (\n          octalStr !== \"0\" ||\n          next === charCodes.digit8 ||\n          next === charCodes.digit9\n        ) {\n          if (inTemplate) {\n            return res(null);\n          } else {\n            errors.strictNumericEscape(startPos, lineStart, curLine);\n          }\n        }\n\n        return res(String.fromCharCode(octal));\n      }\n\n      return res(String.fromCharCode(ch));\n  }\n}\n\ntype HexCharErrorHandlers = IntErrorHandlers & {\n  invalidEscapeSequence(pos: number, lineStart: number, curLine: number): void;\n};\n\n// Used to read character escape sequences ('\\x', '\\u').\nfunction readHexChar(\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  len: number,\n  forceLen: boolean,\n  throwOnInvalid: boolean,\n  errors: HexCharErrorHandlers,\n) {\n  const initialPos = pos;\n  let n;\n  ({ n, pos } = readInt(\n    input,\n    pos,\n    lineStart,\n    curLine,\n    16,\n    len,\n    forceLen,\n    false,\n    errors,\n    /* bailOnError */ !throwOnInvalid,\n  ));\n  if (n === null) {\n    if (throwOnInvalid) {\n      errors.invalidEscapeSequence(initialPos, lineStart, curLine);\n    } else {\n      pos = initialPos - 1;\n    }\n  }\n  return { code: n, pos };\n}\n\nexport type IntErrorHandlers = {\n  numericSeparatorInEscapeSequence(\n    pos: number,\n    lineStart: number,\n    curLine: number,\n  ): void;\n  unexpectedNumericSeparator(\n    pos: number,\n    lineStart: number,\n    curLine: number,\n  ): void;\n  // It can return \"true\" to indicate that the error was handled\n  // and the int parsing should continue.\n  invalidDigit(\n    pos: number,\n    lineStart: number,\n    curLine: number,\n    radix: number,\n  ): boolean;\n};\n\nexport function readInt(\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  radix: number,\n  len: number | undefined,\n  forceLen: boolean,\n  allowNumSeparator: boolean | \"bail\",\n  errors: IntErrorHandlers,\n  bailOnError: boolean,\n) {\n  const start = pos;\n  const forbiddenSiblings =\n    radix === 16\n      ? forbiddenNumericSeparatorSiblings.hex\n      : forbiddenNumericSeparatorSiblings.decBinOct;\n  const isAllowedSibling =\n    radix === 16\n      ? isAllowedNumericSeparatorSibling.hex\n      : radix === 10\n        ? isAllowedNumericSeparatorSibling.dec\n        : radix === 8\n          ? isAllowedNumericSeparatorSibling.oct\n          : isAllowedNumericSeparatorSibling.bin;\n\n  let invalid = false;\n  let total = 0;\n\n  for (let i = 0, e = len == null ? Infinity : len; i < e; ++i) {\n    const code = input.charCodeAt(pos);\n    let val;\n\n    if (code === charCodes.underscore && allowNumSeparator !== \"bail\") {\n      const prev = input.charCodeAt(pos - 1);\n      const next = input.charCodeAt(pos + 1);\n\n      if (!allowNumSeparator) {\n        if (bailOnError) return { n: null, pos };\n        errors.numericSeparatorInEscapeSequence(pos, lineStart, curLine);\n      } else if (\n        Number.isNaN(next) ||\n        !isAllowedSibling(next) ||\n        forbiddenSiblings.has(prev) ||\n        forbiddenSiblings.has(next)\n      ) {\n        if (bailOnError) return { n: null, pos };\n        errors.unexpectedNumericSeparator(pos, lineStart, curLine);\n      }\n\n      // Ignore this _ character\n      ++pos;\n      continue;\n    }\n\n    if (code >= charCodes.lowercaseA) {\n      val = code - charCodes.lowercaseA + charCodes.lineFeed;\n    } else if (code >= charCodes.uppercaseA) {\n      val = code - charCodes.uppercaseA + charCodes.lineFeed;\n    } else if (charCodes.isDigit(code)) {\n      val = code - charCodes.digit0; // 0-9\n    } else {\n      val = Infinity;\n    }\n    if (val >= radix) {\n      // If we found a digit which is too big, errors.invalidDigit can return true to avoid\n      // breaking the loop (this is used for error recovery).\n      if (val <= 9 && bailOnError) {\n        return { n: null, pos };\n      } else if (\n        val <= 9 &&\n        errors.invalidDigit(pos, lineStart, curLine, radix)\n      ) {\n        val = 0;\n      } else if (forceLen) {\n        val = 0;\n        invalid = true;\n      } else {\n        break;\n      }\n    }\n    ++pos;\n    total = total * radix + val;\n  }\n  if (pos === start || (len != null && pos - start !== len) || invalid) {\n    return { n: null, pos };\n  }\n\n  return { n: total, pos };\n}\n\nexport type CodePointErrorHandlers = HexCharErrorHandlers & {\n  invalidCodePoint(pos: number, lineStart: number, curLine: number): void;\n};\n\nexport function readCodePoint(\n  input: string,\n  pos: number,\n  lineStart: number,\n  curLine: number,\n  throwOnInvalid: boolean,\n  errors: CodePointErrorHandlers,\n) {\n  const ch = input.charCodeAt(pos);\n  let code;\n\n  if (ch === charCodes.leftCurlyBrace) {\n    ++pos;\n    ({ code, pos } = readHexChar(\n      input,\n      pos,\n      lineStart,\n      curLine,\n      input.indexOf(\"}\", pos) - pos,\n      true,\n      throwOnInvalid,\n      errors,\n    ));\n    ++pos;\n    if (code !== null && code > 0x10ffff) {\n      if (throwOnInvalid) {\n        errors.invalidCodePoint(pos, lineStart, curLine);\n      } else {\n        return { code: null, pos };\n      }\n    }\n  } else {\n    ({ code, pos } = readHexChar(\n      input,\n      pos,\n      lineStart,\n      curLine,\n      4,\n      false,\n      throwOnInvalid,\n      errors,\n    ));\n  }\n  return { code, pos };\n}\n"], "mappings": ";;;;;;;;eAAA,SAASA,OAAOA,CAACC,IAAI,EAAE;EACrB,OAAOA,IAAI,MAAU,IAAIA,IAAI,MAAU;AACzC,CAAC;AAID,MAAMC,iCAAiC,GAAG;EACxCC,SAAS,EAAE,IAAIC,GAAG,CAAS,kCAS1B,CAAC;EACFC,GAAG,EAAE,IAAID,GAAG,CAAS,iBAKpB;AACH,CAAC;AAED,MAAME,gCAAgC,GAAG;EAEvCC,GAAG,EAAGC,EAAU,IAAKA,EAAE,OAAqB,IAAIA,EAAE,OAAqB;EAGvEC,GAAG,EAAGD,EAAU,IAAKA,EAAE,MAAoB,IAAIA,EAAE,MAAoB;EAGrEE,GAAG,EAAGF,EAAU,IAAKA,EAAE,MAAoB,IAAIA,EAAE,MAAoB;EAGrEH,GAAG,EAAGG,EAAU,IACbA,EAAE,MAAoB,IAAIA,EAAE,MAAoB,IAChDA,EAAE,MAAwB,IAAIA,EAAE,MAAyB,IACzDA,EAAE,MAAwB,IAAIA,EAAE;AACrC,CAAC;AAUM,SAASG,kBAAkBA,CAChCC,IAAsC,EACtCC,KAAa,EACbC,GAAW,EACXC,SAAiB,EACjBC,OAAe,EACfC,MAAmC,EACnC;EACA,MAAMC,UAAU,GAAGJ,GAAG;EACtB,MAAMK,gBAAgB,GAAGJ,SAAS;EAClC,MAAMK,cAAc,GAAGJ,OAAO;EAE9B,IAAIK,GAAG,GAAG,EAAE;EACZ,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAIC,UAAU,GAAGT,GAAG;EACpB,MAAM;IAAEU;EAAO,CAAC,GAAGX,KAAK;EACxB,SAAS;IACP,IAAIC,GAAG,IAAIU,MAAM,EAAE;MACjBP,MAAM,CAACQ,YAAY,CAACP,UAAU,EAAEC,gBAAgB,EAAEC,cAAc,CAAC;MACjEC,GAAG,IAAIR,KAAK,CAACa,KAAK,CAACH,UAAU,EAAET,GAAG,CAAC;MACnC;IACF;IACA,MAAMN,EAAE,GAAGK,KAAK,CAACc,UAAU,CAACb,GAAG,CAAC;IAChC,IAAIc,WAAW,CAAChB,IAAI,EAAEJ,EAAE,EAAEK,KAAK,EAAEC,GAAG,CAAC,EAAE;MACrCO,GAAG,IAAIR,KAAK,CAACa,KAAK,CAACH,UAAU,EAAET,GAAG,CAAC;MACnC;IACF;IACA,IAAIN,EAAE,OAAwB,EAAE;MAC9Ba,GAAG,IAAIR,KAAK,CAACa,KAAK,CAACH,UAAU,EAAET,GAAG,CAAC;MACnC,MAAMe,GAAG,GAAGC,eAAe,CACzBjB,KAAK,EACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPJ,IAAI,KAAK,UAAU,EACnBK,MACF,CAAC;MACD,IAAIY,GAAG,CAACrB,EAAE,KAAK,IAAI,IAAI,CAACc,eAAe,EAAE;QACvCA,eAAe,GAAG;UAAER,GAAG;UAAEC,SAAS;UAAEC;QAAQ,CAAC;MAC/C,CAAC,MAAM;QACLK,GAAG,IAAIQ,GAAG,CAACrB,EAAE;MACf;MACA,CAAC;QAAEM,GAAG;QAAEC,SAAS;QAAEC;MAAQ,CAAC,GAAGa,GAAG;MAClCN,UAAU,GAAGT,GAAG;IAClB,CAAC,MAAM,IACLN,EAAE,SAA4B,IAC9BA,EAAE,SAAiC,EACnC;MACA,EAAEM,GAAG;MACL,EAAEE,OAAO;MACTD,SAAS,GAAGD,GAAG;IACjB,CAAC,MAAM,IAAIN,EAAE,OAAuB,IAAIA,EAAE,OAA6B,EAAE;MACvE,IAAII,IAAI,KAAK,UAAU,EAAE;QACvBS,GAAG,IAAIR,KAAK,CAACa,KAAK,CAACH,UAAU,EAAET,GAAG,CAAC,GAAG,IAAI;QAC1C,EAAEA,GAAG;QACL,IACEN,EAAE,OAA6B,IAC/BK,KAAK,CAACc,UAAU,CAACb,GAAG,CAAC,OAAuB,EAC5C;UACA,EAAEA,GAAG;QACP;QACA,EAAEE,OAAO;QACTO,UAAU,GAAGR,SAAS,GAAGD,GAAG;MAC9B,CAAC,MAAM;QACLG,MAAM,CAACQ,YAAY,CAACP,UAAU,EAAEC,gBAAgB,EAAEC,cAAc,CAAC;MACnE;IACF,CAAC,MAAM;MACL,EAAEN,GAAG;IACP;EACF;EACA,OAEI;IACEA,GAAG;IACHiB,GAAG,EAAEV,GAAG;IACRC,eAAe;IACfP,SAAS;IACTC,OAAO;IACPgB,eAAe,EAAE,CAAC,CAACV;EACrB,CAAC;AACP;AAEA,SAASM,WAAWA,CAClBhB,IAAsC,EACtCJ,EAAU,EACVK,KAAa,EACbC,GAAW,EACX;EACA,IAAIF,IAAI,KAAK,UAAU,EAAE;IACvB,OACEJ,EAAE,OAA0B,IAC3BA,EAAE,OAAyB,IAC1BK,KAAK,CAACc,UAAU,CAACb,GAAG,GAAG,CAAC,CAAC,QAA8B;EAE7D;EACA,OACEN,EAAE,MAAMI,IAAI,KAAK,QAAQ,UAAiD,CAAC;AAE/E;AAOA,SAASkB,eAAeA,CACtBjB,KAAa,EACbC,GAAW,EACXC,SAAiB,EACjBC,OAAe,EACfiB,UAAmB,EACnBhB,MAAgC,EAChC;EACA,MAAMiB,cAAc,GAAG,CAACD,UAAU;EAClCnB,GAAG,EAAE;EAEL,MAAMe,GAAG,GAAIrB,EAAiB,KAAM;IAAEM,GAAG;IAAEN,EAAE;IAAEO,SAAS;IAAEC;EAAQ,CAAC,CAAC;EAEpE,MAAMR,EAAE,GAAGK,KAAK,CAACc,UAAU,CAACb,GAAG,EAAE,CAAC;EAClC,QAAQN,EAAE;IACR;MACE,OAAOqB,GAAG,CAAC,IAAI,CAAC;IAClB;MACE,OAAOA,GAAG,CAAC,IAAI,CAAC;IAClB;MAA2B;QACzB,IAAI5B,IAAI;QACR,CAAC;UAAEA,IAAI;UAAEa;QAAI,CAAC,GAAGqB,WAAW,CAC1BtB,KAAK,EACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACP,CAAC,EACD,KAAK,EACLkB,cAAc,EACdjB,MACF,CAAC;QACD,OAAOY,GAAG,CAAC5B,IAAI,KAAK,IAAI,GAAG,IAAI,GAAGmC,MAAM,CAACC,YAAY,CAACpC,IAAI,CAAC,CAAC;MAC9D;IACA;MAA2B;QACzB,IAAIA,IAAI;QACR,CAAC;UAAEA,IAAI;UAAEa;QAAI,CAAC,GAAGwB,aAAa,CAC5BzB,KAAK,EACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPkB,cAAc,EACdjB,MACF,CAAC;QACD,OAAOY,GAAG,CAAC5B,IAAI,KAAK,IAAI,GAAG,IAAI,GAAGmC,MAAM,CAACG,aAAa,CAACtC,IAAI,CAAC,CAAC;MAC/D;IACA;MACE,OAAO4B,GAAG,CAAC,IAAI,CAAC;IAClB;MACE,OAAOA,GAAG,CAAC,IAAI,CAAC;IAClB;MACE,OAAOA,GAAG,CAAC,QAAQ,CAAC;IACtB;MACE,OAAOA,GAAG,CAAC,IAAI,CAAC;IAClB;MACE,IAAIhB,KAAK,CAACc,UAAU,CAACb,GAAG,CAAC,OAAuB,EAAE;QAChD,EAAEA,GAAG;MACP;IAEF;MACEC,SAAS,GAAGD,GAAG;MACf,EAAEE,OAAO;IAEX;IACA;MACE,OAAOa,GAAG,CAAC,EAAE,CAAC;IAChB;IACA;MACE,IAAII,UAAU,EAAE;QACd,OAAOJ,GAAG,CAAC,IAAI,CAAC;MAClB,CAAC,MAAM;QACLZ,MAAM,CAACuB,mBAAmB,CAAC1B,GAAG,GAAG,CAAC,EAAEC,SAAS,EAAEC,OAAO,CAAC;MACzD;IAEF;MACE,IAAIR,EAAE,MAAoB,IAAIA,EAAE,MAAoB,EAAE;QACpD,MAAMiC,QAAQ,GAAG3B,GAAG,GAAG,CAAC;QACxB,MAAM4B,KAAK,GAAG,SAAS,CAACC,IAAI,CAAC9B,KAAK,CAACa,KAAK,CAACe,QAAQ,EAAE3B,GAAG,GAAG,CAAC,CAAC,CAAC;QAE5D,IAAI8B,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC;QAEvB,IAAIG,KAAK,GAAGC,QAAQ,CAACF,QAAQ,EAAE,CAAC,CAAC;QACjC,IAAIC,KAAK,GAAG,GAAG,EAAE;UACfD,QAAQ,GAAGA,QAAQ,CAAClB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChCmB,KAAK,GAAGC,QAAQ,CAACF,QAAQ,EAAE,CAAC,CAAC;QAC/B;QACA9B,GAAG,IAAI8B,QAAQ,CAACpB,MAAM,GAAG,CAAC;QAC1B,MAAMuB,IAAI,GAAGlC,KAAK,CAACc,UAAU,CAACb,GAAG,CAAC;QAClC,IACE8B,QAAQ,KAAK,GAAG,IAChBG,IAAI,OAAqB,IACzBA,IAAI,OAAqB,EACzB;UACA,IAAId,UAAU,EAAE;YACd,OAAOJ,GAAG,CAAC,IAAI,CAAC;UAClB,CAAC,MAAM;YACLZ,MAAM,CAACuB,mBAAmB,CAACC,QAAQ,EAAE1B,SAAS,EAAEC,OAAO,CAAC;UAC1D;QACF;QAEA,OAAOa,GAAG,CAACO,MAAM,CAACC,YAAY,CAACQ,KAAK,CAAC,CAAC;MACxC;MAEA,OAAOhB,GAAG,CAACO,MAAM,CAACC,YAAY,CAAC7B,EAAE,CAAC,CAAC;EACvC;AACF;AAOA,SAAS2B,WAAWA,CAClBtB,KAAa,EACbC,GAAW,EACXC,SAAiB,EACjBC,OAAe,EACfgC,GAAW,EACXC,QAAiB,EACjBf,cAAuB,EACvBjB,MAA4B,EAC5B;EACA,MAAMC,UAAU,GAAGJ,GAAG;EACtB,IAAIoC,CAAC;EACL,CAAC;IAAEA,CAAC;IAAEpC;EAAI,CAAC,GAAGqC,OAAO,CACnBtC,KAAK,EACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACP,EAAE,EACFgC,GAAG,EACHC,QAAQ,EACR,KAAK,EACLhC,MAAM,EACY,CAACiB,cACrB,CAAC;EACD,IAAIgB,CAAC,KAAK,IAAI,EAAE;IACd,IAAIhB,cAAc,EAAE;MAClBjB,MAAM,CAACmC,qBAAqB,CAAClC,UAAU,EAAEH,SAAS,EAAEC,OAAO,CAAC;IAC9D,CAAC,MAAM;MACLF,GAAG,GAAGI,UAAU,GAAG,CAAC;IACtB;EACF;EACA,OAAO;IAAEjB,IAAI,EAAEiD,CAAC;IAAEpC;EAAI,CAAC;AACzB;AAuBO,SAASqC,OAAOA,CACrBtC,KAAa,EACbC,GAAW,EACXC,SAAiB,EACjBC,OAAe,EACfqC,KAAa,EACbL,GAAuB,EACvBC,QAAiB,EACjBK,iBAAmC,EACnCrC,MAAwB,EACxBsC,WAAoB,EACpB;EACA,MAAMC,KAAK,GAAG1C,GAAG;EACjB,MAAM2C,iBAAiB,GACrBJ,KAAK,KAAK,EAAE,GACRnD,iCAAiC,CAACG,GAAG,GACrCH,iCAAiC,CAACC,SAAS;EACjD,MAAMuD,gBAAgB,GACpBL,KAAK,KAAK,EAAE,GACR/C,gCAAgC,CAACD,GAAG,GACpCgD,KAAK,KAAK,EAAE,GACV/C,gCAAgC,CAACI,GAAG,GACpC2C,KAAK,KAAK,CAAC,GACT/C,gCAAgC,CAACG,GAAG,GACpCH,gCAAgC,CAACC,GAAG;EAE9C,IAAIoD,OAAO,GAAG,KAAK;EACnB,IAAIC,KAAK,GAAG,CAAC;EAEb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGd,GAAG,IAAI,IAAI,GAAGe,QAAQ,GAAGf,GAAG,EAAEa,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IAC5D,MAAM5D,IAAI,GAAGY,KAAK,CAACc,UAAU,CAACb,GAAG,CAAC;IAClC,IAAIkD,GAAG;IAEP,IAAI/D,IAAI,OAAyB,IAAIqD,iBAAiB,KAAK,MAAM,EAAE;MACjE,MAAMW,IAAI,GAAGpD,KAAK,CAACc,UAAU,CAACb,GAAG,GAAG,CAAC,CAAC;MACtC,MAAMiC,IAAI,GAAGlC,KAAK,CAACc,UAAU,CAACb,GAAG,GAAG,CAAC,CAAC;MAEtC,IAAI,CAACwC,iBAAiB,EAAE;QACtB,IAAIC,WAAW,EAAE,OAAO;UAAEL,CAAC,EAAE,IAAI;UAAEpC;QAAI,CAAC;QACxCG,MAAM,CAACiD,gCAAgC,CAACpD,GAAG,EAAEC,SAAS,EAAEC,OAAO,CAAC;MAClE,CAAC,MAAM,IACLmD,MAAM,CAACC,KAAK,CAACrB,IAAI,CAAC,IAClB,CAACW,gBAAgB,CAACX,IAAI,CAAC,IACvBU,iBAAiB,CAACY,GAAG,CAACJ,IAAI,CAAC,IAC3BR,iBAAiB,CAACY,GAAG,CAACtB,IAAI,CAAC,EAC3B;QACA,IAAIQ,WAAW,EAAE,OAAO;UAAEL,CAAC,EAAE,IAAI;UAAEpC;QAAI,CAAC;QACxCG,MAAM,CAACqD,0BAA0B,CAACxD,GAAG,EAAEC,SAAS,EAAEC,OAAO,CAAC;MAC5D;MAGA,EAAEF,GAAG;MACL;IACF;IAEA,IAAIb,IAAI,MAAwB,EAAE;MAChC+D,GAAG,GAAG/D,IAAI,KAAuB,KAAqB;IACxD,CAAC,MAAM,IAAIA,IAAI,MAAwB,EAAE;MACvC+D,GAAG,GAAG/D,IAAI,KAAuB,KAAqB;IACxD,CAAC,MAAM,IAAIsE,QAAA,CAAkBtE,IAAI,CAAC,EAAE;MAClC+D,GAAG,GAAG/D,IAAI,KAAmB;IAC/B,CAAC,MAAM;MACL+D,GAAG,GAAGD,QAAQ;IAChB;IACA,IAAIC,GAAG,IAAIX,KAAK,EAAE;MAGhB,IAAIW,GAAG,IAAI,CAAC,IAAIT,WAAW,EAAE;QAC3B,OAAO;UAAEL,CAAC,EAAE,IAAI;UAAEpC;QAAI,CAAC;MACzB,CAAC,MAAM,IACLkD,GAAG,IAAI,CAAC,IACR/C,MAAM,CAACuD,YAAY,CAAC1D,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAEqC,KAAK,CAAC,EACnD;QACAW,GAAG,GAAG,CAAC;MACT,CAAC,MAAM,IAAIf,QAAQ,EAAE;QACnBe,GAAG,GAAG,CAAC;QACPL,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM;QACL;MACF;IACF;IACA,EAAE7C,GAAG;IACL8C,KAAK,GAAGA,KAAK,GAAGP,KAAK,GAAGW,GAAG;EAC7B;EACA,IAAIlD,GAAG,KAAK0C,KAAK,IAAKR,GAAG,IAAI,IAAI,IAAIlC,GAAG,GAAG0C,KAAK,KAAKR,GAAI,IAAIW,OAAO,EAAE;IACpE,OAAO;MAAET,CAAC,EAAE,IAAI;MAAEpC;IAAI,CAAC;EACzB;EAEA,OAAO;IAAEoC,CAAC,EAAEU,KAAK;IAAE9C;EAAI,CAAC;AAC1B;AAMO,SAASwB,aAAaA,CAC3BzB,KAAa,EACbC,GAAW,EACXC,SAAiB,EACjBC,OAAe,EACfkB,cAAuB,EACvBjB,MAA8B,EAC9B;EACA,MAAMT,EAAE,GAAGK,KAAK,CAACc,UAAU,CAACb,GAAG,CAAC;EAChC,IAAIb,IAAI;EAER,IAAIO,EAAE,QAA6B,EAAE;IACnC,EAAEM,GAAG;IACL,CAAC;MAAEb,IAAI;MAAEa;IAAI,CAAC,GAAGqB,WAAW,CAC1BtB,KAAK,EACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPH,KAAK,CAAC4D,OAAO,CAAC,GAAG,EAAE3D,GAAG,CAAC,GAAGA,GAAG,EAC7B,IAAI,EACJoB,cAAc,EACdjB,MACF,CAAC;IACD,EAAEH,GAAG;IACL,IAAIb,IAAI,KAAK,IAAI,IAAIA,IAAI,GAAG,QAAQ,EAAE;MACpC,IAAIiC,cAAc,EAAE;QAClBjB,MAAM,CAACyD,gBAAgB,CAAC5D,GAAG,EAAEC,SAAS,EAAEC,OAAO,CAAC;MAClD,CAAC,MAAM;QACL,OAAO;UAAEf,IAAI,EAAE,IAAI;UAAEa;QAAI,CAAC;MAC5B;IACF;EACF,CAAC,MAAM;IACL,CAAC;MAAEb,IAAI;MAAEa;IAAI,CAAC,GAAGqB,WAAW,CAC1BtB,KAAK,EACLC,GAAG,EACHC,SAAS,EACTC,OAAO,EACP,CAAC,EACD,KAAK,EACLkB,cAAc,EACdjB,MACF,CAAC;EACH;EACA,OAAO;IAAEhB,IAAI;IAAEa;EAAI,CAAC;AACtB", "ignoreList": []}