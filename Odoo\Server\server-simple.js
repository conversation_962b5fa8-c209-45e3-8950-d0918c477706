import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());

// Test route
app.get('/test', (req, res) => {
    res.json({ message: 'Skill Swap Platform Server is working!' });
});

// Simple test route for API
app.get('/api/test', (req, res) => {
    res.json({ success: true, message: 'API is working!' });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
    console.log(`Skill Swap Platform Server running at http://localhost:${PORT}`);
    console.log('🚀 Server ready to facilitate skill exchanges!');
});
