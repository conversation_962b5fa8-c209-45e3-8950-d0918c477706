/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Ccta-section.tsx%22%2C%22ids%22%3A%5B%22CTASection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Cfeatures-section.tsx%22%2C%22ids%22%3A%5B%22FeaturesSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Chow-it-works-section.tsx%22%2C%22ids%22%3A%5B%22HowItWorksSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Ctestimonials-section.tsx%22%2C%22ids%22%3A%5B%22TestimonialsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%22Loading%22%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Ccta-section.tsx%22%2C%22ids%22%3A%5B%22CTASection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Cfeatures-section.tsx%22%2C%22ids%22%3A%5B%22FeaturesSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Chow-it-works-section.tsx%22%2C%22ids%22%3A%5B%22HowItWorksSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Ctestimonials-section.tsx%22%2C%22ids%22%3A%5B%22TestimonialsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%22Loading%22%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/landing/cta-section.tsx */ \"(app-pages-browser)/./src/components/landing/cta-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/landing/features-section.tsx */ \"(app-pages-browser)/./src/components/landing/features-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/landing/how-it-works-section.tsx */ \"(app-pages-browser)/./src/components/landing/how-it-works-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/landing/testimonials-section.tsx */ \"(app-pages-browser)/./src/components/landing/testimonials-section.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/header.tsx */ \"(app-pages-browser)/./src/components/layout/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/loading.tsx */ \"(app-pages-browser)/./src/components/ui/loading.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0hhY2thdGhvbiU1QyU1Q09kb28lNUMlNUNPZG9vJTVDJTVDQ2xpZW50JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMl9fZXNNb2R1bGUlMjIlMkMlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNIYWNrYXRob24lNUMlNUNPZG9vJTVDJTVDT2RvbyU1QyU1Q0NsaWVudCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYW5kaW5nJTVDJTVDY3RhLXNlY3Rpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQ1RBU2VjdGlvbiUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDSGFja2F0aG9uJTVDJTVDT2RvbyU1QyU1Q09kb28lNUMlNUNDbGllbnQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDbGFuZGluZyU1QyU1Q2ZlYXR1cmVzLXNlY3Rpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyRmVhdHVyZXNTZWN0aW9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNIYWNrYXRob24lNUMlNUNPZG9vJTVDJTVDT2RvbyU1QyU1Q0NsaWVudCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYW5kaW5nJTVDJTVDaG93LWl0LXdvcmtzLXNlY3Rpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySG93SXRXb3Jrc1NlY3Rpb24lMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q0hhY2thdGhvbiU1QyU1Q09kb28lNUMlNUNPZG9vJTVDJTVDQ2xpZW50JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2xhbmRpbmclNUMlNUN0ZXN0aW1vbmlhbHMtc2VjdGlvbi50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUZXN0aW1vbmlhbHNTZWN0aW9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNIYWNrYXRob24lNUMlNUNPZG9vJTVDJTVDT2RvbyU1QyU1Q0NsaWVudCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNoZWFkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySGVhZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNIYWNrYXRob24lNUMlNUNPZG9vJTVDJTVDT2RvbyU1QyU1Q0NsaWVudCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN1aSU1QyU1Q2xvYWRpbmcudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTG9hZGluZyUyMiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhOQUFvSztBQUNwSztBQUNBLDBNQUFpSjtBQUNqSjtBQUNBLG9OQUEySjtBQUMzSjtBQUNBLDROQUFpSztBQUNqSztBQUNBLDROQUFtSztBQUNuSztBQUNBLDhMQUF1STtBQUN2STtBQUNBLHdMQUFxSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiX19lc01vZHVsZVwiLFwiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXEhhY2thdGhvblxcXFxPZG9vXFxcXE9kb29cXFxcQ2xpZW50XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGFwcC1kaXJcXFxcbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ1RBU2VjdGlvblwiXSAqLyBcIkM6XFxcXEhhY2thdGhvblxcXFxPZG9vXFxcXE9kb29cXFxcQ2xpZW50XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxhbmRpbmdcXFxcY3RhLXNlY3Rpb24udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJGZWF0dXJlc1NlY3Rpb25cIl0gKi8gXCJDOlxcXFxIYWNrYXRob25cXFxcT2Rvb1xcXFxPZG9vXFxcXENsaWVudFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxsYW5kaW5nXFxcXGZlYXR1cmVzLXNlY3Rpb24udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJIb3dJdFdvcmtzU2VjdGlvblwiXSAqLyBcIkM6XFxcXEhhY2thdGhvblxcXFxPZG9vXFxcXE9kb29cXFxcQ2xpZW50XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxhbmRpbmdcXFxcaG93LWl0LXdvcmtzLXNlY3Rpb24udHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUZXN0aW1vbmlhbHNTZWN0aW9uXCJdICovIFwiQzpcXFxcSGFja2F0aG9uXFxcXE9kb29cXFxcT2Rvb1xcXFxDbGllbnRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcbGFuZGluZ1xcXFx0ZXN0aW1vbmlhbHMtc2VjdGlvbi50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkhlYWRlclwiXSAqLyBcIkM6XFxcXEhhY2thdGhvblxcXFxPZG9vXFxcXE9kb29cXFxcQ2xpZW50XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxheW91dFxcXFxoZWFkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJMb2FkaW5nXCJdICovIFwiQzpcXFxcSGFja2F0aG9uXFxcXE9kb29cXFxcT2Rvb1xcXFxDbGllbnRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcbG9hZGluZy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Ccta-section.tsx%22%2C%22ids%22%3A%5B%22CTASection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Cfeatures-section.tsx%22%2C%22ids%22%3A%5B%22FeaturesSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Chow-it-works-section.tsx%22%2C%22ids%22%3A%5B%22HowItWorksSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5Ctestimonials-section.tsx%22%2C%22ids%22%3A%5B%22TestimonialsSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Cheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CHackathon%5C%5COdoo%5C%5COdoo%5C%5CClient%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cloading.tsx%22%2C%22ids%22%3A%5B%22Loading%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/landing/testimonials-section.tsx":
/*!*********************************************************!*\
  !*** ./src/components/landing/testimonials-section.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TestimonialsSection: () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _ui_section__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/section */ \"(app-pages-browser)/./src/components/ui/section.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_animated_section__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/animated-section */ \"(app-pages-browser)/./src/components/ui/animated-section.tsx\");\n/* __next_internal_client_entry_do_not_use__ TestimonialsSection auto */ \n\n\n\nconst testimonials = [\n    {\n        name: 'Sarah Chen',\n        role: 'UX Designer',\n        avatar: '/avatars/sarah.jpg',\n        rating: 5,\n        content: \"I learned Spanish conversation skills in exchange for teaching Figma. The process was so smooth and natural - like having a coffee chat with a friend!\",\n        skills: [\n            'Figma',\n            'Design Systems'\n        ],\n        learned: [\n            'Spanish',\n            'Photography'\n        ]\n    },\n    {\n        name: 'Marcus Rodriguez',\n        role: 'Software Developer',\n        avatar: '/avatars/marcus.jpg',\n        rating: 5,\n        content: \"SkillSwap connected me with amazing people. I taught React and learned guitar basics. The AI matching is incredibly accurate!\",\n        skills: [\n            'React',\n            'Node.js'\n        ],\n        learned: [\n            'Guitar',\n            'Music Theory'\n        ]\n    },\n    {\n        name: 'Emily Johnson',\n        role: 'Marketing Manager',\n        avatar: '/avatars/emily.jpg',\n        rating: 5,\n        content: \"As someone who's always wanted to learn coding, finding a patient teacher through SkillSwap was a game-changer. I taught social media marketing in return.\",\n        skills: [\n            'Social Media',\n            'Content Strategy'\n        ],\n        learned: [\n            'Python',\n            'Data Analysis'\n        ]\n    },\n    {\n        name: 'David Kim',\n        role: 'Chef',\n        avatar: '/avatars/david.jpg',\n        rating: 5,\n        content: \"I never thought I'd learn graphic design, but SkillSwap made it possible. Taught cooking in exchange for Photoshop skills. Win-win!\",\n        skills: [\n            'Cooking',\n            'Recipe Development'\n        ],\n        learned: [\n            'Photoshop',\n            'Branding'\n        ]\n    },\n    {\n        name: 'Anna Kowalski',\n        role: 'Financial Analyst',\n        avatar: '/avatars/anna.jpg',\n        rating: 5,\n        content: \"The platform is incredibly user-friendly. I've completed 5 skill swaps and made genuine friends along the way. The community is amazing!\",\n        skills: [\n            'Excel',\n            'Financial Modeling'\n        ],\n        learned: [\n            'Web Development',\n            'SEO'\n        ]\n    },\n    {\n        name: 'James Thompson',\n        role: 'Teacher',\n        avatar: '/avatars/james.jpg',\n        rating: 5,\n        content: \"Teaching language skills while learning digital marketing has been transformative for my career. SkillSwap makes peer learning feel natural.\",\n        skills: [\n            'French',\n            'Language Teaching'\n        ],\n        learned: [\n            'Digital Marketing',\n            'Analytics'\n        ]\n    }\n];\nfunction TestimonialsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_section__WEBPACK_IMPORTED_MODULE_1__.Section, {\n        background: \"white\",\n        padding: \"xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_section__WEBPACK_IMPORTED_MODULE_1__.SectionHeader, {\n                title: \"Loved by skill enthusiasts worldwide\",\n                description: \"Real stories from real people who've transformed their skills through our platform.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_section__WEBPACK_IMPORTED_MODULE_1__.SectionGrid, {\n                columns: 3,\n                gap: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedContainer, {\n                    staggerChildren: 0.1,\n                    children: testimonials.map((testimonial)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.TestimonialCard, {\n                                name: testimonial.name,\n                                role: testimonial.role,\n                                content: testimonial.content,\n                                rating: testimonial.rating,\n                                animated: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this)\n                        }, testimonial.name, false, {\n                            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\landing\\\\testimonials-section.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_c = TestimonialsSection;\nvar _c;\n$RefreshReg$(_c, \"TestimonialsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/testimonials-section.tsx\n"));

/***/ })

});