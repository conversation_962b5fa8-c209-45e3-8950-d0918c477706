{"version": 3, "names": ["_helperCompilationTargets", "data", "require", "resolveBrowserslistConfigFile", "browserslistConfigFile", "config<PERSON><PERSON><PERSON><PERSON>", "undefined", "resolveTargets", "options", "root", "optTargets", "targets", "Array", "isArray", "browsers", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "getTargets", "ignoreBrowserslistConfig", "browserslistEnv"], "sources": ["../../src/config/resolve-targets-browser.ts"], "sourcesContent": ["/* c8 ignore start */\n\nimport type { ValidatedOptions } from \"./validation/options.ts\";\nimport getTargets, {\n  type InputTargets,\n} from \"@babel/helper-compilation-targets\";\n\nimport type { Targets } from \"@babel/helper-compilation-targets\";\n\nexport function resolveBrowserslistConfigFile(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  browserslistConfigFile: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  configFilePath: string,\n): string | void {\n  return undefined;\n}\n\nexport function resolveTargets(\n  options: ValidatedOptions,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  root: string,\n): Targets {\n  const optTargets = options.targets;\n  let targets: InputTargets;\n\n  if (typeof optTargets === \"string\" || Array.isArray(optTargets)) {\n    targets = { browsers: optTargets };\n  } else if (optTargets) {\n    if (\"esmodules\" in optTargets) {\n      targets = { ...optTargets, esmodules: \"intersect\" };\n    } else {\n      // https://github.com/microsoft/TypeScript/issues/17002\n      targets = optTargets as InputTargets;\n    }\n  }\n\n  return getTargets(targets, {\n    ignoreBrowserslistConfig: true,\n    browserslistEnv: options.browserslistEnv,\n  });\n}\n"], "mappings": ";;;;;;;AAGA,SAAAA,0BAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,yBAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAMO,SAASE,6BAA6BA,CAE3CC,sBAA8B,EAE9BC,cAAsB,EACP;EACf,OAAOC,SAAS;AAClB;AAEO,SAASC,cAAcA,CAC5BC,OAAyB,EAEzBC,IAAY,EACH;EACT,MAAMC,UAAU,GAAGF,OAAO,CAACG,OAAO;EAClC,IAAIA,OAAqB;EAEzB,IAAI,OAAOD,UAAU,KAAK,QAAQ,IAAIE,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAE;IAC/DC,OAAO,GAAG;MAAEG,QAAQ,EAAEJ;IAAW,CAAC;EACpC,CAAC,MAAM,IAAIA,UAAU,EAAE;IACrB,IAAI,WAAW,IAAIA,UAAU,EAAE;MAC7BC,OAAO,GAAAI,MAAA,CAAAC,MAAA,KAAQN,UAAU;QAAEO,SAAS,EAAE;MAAW,EAAE;IACrD,CAAC,MAAM;MAELN,OAAO,GAAGD,UAA0B;IACtC;EACF;EAEA,OAAO,IAAAQ,mCAAU,EAACP,OAAO,EAAE;IACzBQ,wBAAwB,EAAE,IAAI;IAC9BC,eAAe,EAAEZ,OAAO,CAACY;EAC3B,CAAC,CAAC;AACJ;AAAC", "ignoreList": []}