"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/section.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/section.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Section: () => (/* binding */ Section),\n/* harmony export */   SectionGrid: () => (/* binding */ SectionGrid),\n/* harmony export */   SectionHeader: () => (/* binding */ SectionHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _animated_section__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./animated-section */ \"(app-pages-browser)/./src/components/ui/animated-section.tsx\");\n/* __next_internal_client_entry_do_not_use__ Section,SectionHeader,SectionGrid auto */ \n\n\n\nconst backgroundVariants = {\n    white: 'bg-white',\n    gray: 'bg-gray-50',\n    gradient: 'bg-gradient-to-br from-blue-50 via-white to-purple-50',\n    warm: 'bg-warm-200'\n};\nconst paddingVariants = {\n    sm: 'py-12',\n    md: 'py-16',\n    lg: 'py-20',\n    xl: 'py-24'\n};\nconst maxWidthVariants = {\n    sm: 'max-w-sm',\n    md: 'max-w-md',\n    lg: 'max-w-lg',\n    xl: 'max-w-xl',\n    '2xl': 'max-w-2xl',\n    '4xl': 'max-w-4xl',\n    '6xl': 'max-w-6xl',\n    '7xl': 'max-w-7xl',\n    full: 'max-w-full'\n};\nfunction Section(param) {\n    let { children, className, containerClassName, background = 'white', padding = 'lg', animated = true, maxWidth = '7xl', id } = param;\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: id,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(backgroundVariants[background], paddingVariants[padding], className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('mx-auto px-6 lg:px-8', maxWidthVariants[maxWidth], containerClassName),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n    if (animated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedSection, {\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    return content;\n}\n_c = Section;\nfunction SectionHeader(param) {\n    let { title, subtitle, description, centered = true, className, animated = true } = param;\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(centered ? 'text-center' : 'text-left', 'mb-16', className),\n        children: [\n            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm font-semibold text-primary-600 uppercase tracking-wide mb-3\",\n                children: subtitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-6 text-lg leading-8 text-gray-600 max-w-3xl mx-auto\",\n                children: description\n            }, void 0, false, {\n                fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n    if (animated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animated_section__WEBPACK_IMPORTED_MODULE_3__.AnimatedSection, {\n            delay: 0.1,\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    return content;\n}\n_c1 = SectionHeader;\nfunction SectionGrid(param) {\n    let { children, columns = 3, gap = 'lg', className } = param;\n    const columnVariants = {\n        1: 'grid-cols-1',\n        2: 'grid-cols-1 md:grid-cols-2',\n        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n        4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'\n    };\n    const gapVariants = {\n        sm: 'gap-6',\n        md: 'gap-8',\n        lg: 'gap-12'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('grid', columnVariants[columns], gapVariants[gap], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Hackathon\\\\Odoo\\\\Odoo\\\\Client\\\\src\\\\components\\\\ui\\\\section.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_c2 = SectionGrid;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Section\");\n$RefreshReg$(_c1, \"SectionHeader\");\n$RefreshReg$(_c2, \"SectionGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/section.tsx\n"));

/***/ })

});